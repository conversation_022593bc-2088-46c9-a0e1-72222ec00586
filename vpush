#!/bin/zsh

# vpush.sh - Version Bump and Git Tagging Script
# Usage: ./vpush.sh <version> "<change summary>"
# Example: ./vpush.sh 2.9.5 "Fixed error propagation from BrainService"
#
# Description:
#   This script bumps the version by updating the VERSION file,
#   commits the change, creates a Git tag, and pushes both.
#
# Preconditions:
#   - CHANGELOG.md should be updated manually before running this script.
#   - You should have staged or committed any other changes prior to this bump.
#
# Example workflow:
#   1. Edit source files and update CHANGELOG.md
#   2. Run: ./vpush.sh 2.9.5 "Your summary here"
#   3. <PERSON><PERSON>t updates VERSION, commits it, creates and pushes a Git tag.

new_version="$1"
change_summary="$2"

show_help() {
  echo ""
  echo "⚙️  Version bump & push script"
  echo ""
  echo "Usage: ./vpush.sh <version> \"<change summary>\""
  echo "Example: ./vpush.sh 2.9.5 \"Fixed error propagation from BrainService\""
  echo ""
  echo "This script does the following:"
  echo "  1. Updates the VERSION file with the new version number."
  echo "  2. Commits the VERSION file."
  echo "  3. Creates a Git tag (v<version>)."
  echo "  4. Pushes the commit and the tag to the remote repository."
  echo ""
  echo "📋 Before running this script:"
  echo "  - Make sure you’ve manually updated CHANGELOG.md."
  echo "  - Make sure all other changes are committed/staged."
  echo ""
}

check_branch() {
  current_branch=$(git branch --show-current)
  if [[ "$current_branch" != "main" && "$current_branch" != "master" ]]; then
    echo "❌ Error: vpush can only be run on main/master branch"
    echo "Current branch: $current_branch"
    echo "Please checkout main branch first: git checkout main"
    exit 1
  fi
}

check_clean_working_dir() {
  if [[ -n $(git status --porcelain) ]]; then
    echo "❌ Error: Working directory is not clean"
    echo "Please commit or stash changes before running vpush"
    git status --short
    exit 1
  fi
}

if [[ -z "$new_version" || -z "$change_summary" || "$1" == "--help" ]]; then
  show_help
  exit 1
fi

check_branch
check_clean_working_dir

echo "$new_version" > VERSION
git add VERSION
git commit -m "$new_version - $change_summary"
git tag "v$new_version"
git push
git push origin "v$new_version"

echo "✅ VERSION updated, commit and tag pushed: $new_version"
