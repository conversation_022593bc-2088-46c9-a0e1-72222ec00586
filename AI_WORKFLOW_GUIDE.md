# AI Workflow Guide: VoxManifestorApp Development

## Overview

This guide documents the optimal usage of different AI tools and models in the VoxManifestorApp development cycle. Each tool has unique strengths that align with specific development phases and tasks.

## Available AI Tools & Their Strengths

### 1. **Cursor IDE** (Primary Development Environment)
- **Strengths**: Real-time code assistance, context-aware suggestions, rapid iteration
- **Access**: Unlimited during development sessions
- **Best For**: Active coding, debugging, refactoring

### 2. **Claude Code** (Strategic Development Partner)
- **Strengths**: Big-picture analysis, architectural roadmapping, strategic planning, high-level summarization, systematic problem decomposition
- **Access**: Unlimited - Strategic development partner
- **Best For**: Morning planning sessions, architectural strategy, priority assessment, blocker resolution, progress analysis
- **Interface**: NeoVim/command line (complementing Cursor IDE)

### 3. **Augment** (Deep Context Analysis)
- **Strengths**: Complex codebase analysis, architectural insights, pattern recognition
- **Access**: Limited (1-2 queries/day)
- **Best For**: High-level architectural decisions, complex debugging

### 4. **Gemini** (AI Integration & Dialogue Logic)
- **Strengths**: Natural language processing, conversation design, AI system integration
- **Access**: Limited requests
- **Best For**: Conversational AI features, dialogue system design

## Development Phase Workflows

### Phase 1: Planning & Requirements (PLANNER Mode)

#### **Primary Tool: Cursor IDE**
- **Tasks**: PRD creation, scratchpad planning, requirement analysis
- **Workflow**: 
  1. Create/update PRDs in `agent/planning/` directory
  2. Use scratchpad (`agent/scratchpad.md`) for iterative planning
  3. Document architectural decisions

#### **Secondary Tool: Augment** (Strategic Use)
- **When to Use**: Complex architectural decisions, major refactoring plans
- **Example Query**: "Analyze the ConversationAgent.kt architecture and suggest optimal decomposition strategy"
- **Timing**: Early in planning phase for maximum impact

#### **Output**: Detailed PRDs, architectural plans, task breakdowns

### Phase 2: Implementation (EXECUTOR Mode)

#### **Primary Tool: Cursor IDE**
- **Tasks**: Active coding, real-time debugging, iterative development
- **Workflow**:
  1. Implement features using Cursor's context-aware suggestions
  2. Use inline code completion for rapid development
  3. Leverage real-time error detection and fixes

#### **Secondary Tool: Claude Code**
- **When to Use**: File operations, git workflow, systematic refactoring
- **Example Tasks**: 
  - Multi-file refactoring operations
  - Git commit workflows
  - Dependency management
  - Build system operations

#### **Output**: Implemented features, working code, proper version control

### Phase 3: AI Integration & Dialogue Design

#### **Primary Tool: Gemini**
- **Tasks**: Conversational AI logic, dialogue system design, prompt engineering
- **Workflow**:
  1. Design conversation flows for check-in system
  2. Optimize prompts for BrainService integration
  3. Refine dialogue chain architecture
  4. Test conversational patterns

#### **Secondary Tool: Cursor IDE**
- **Tasks**: Implementing Gemini-designed patterns into code
- **Integration**: Translate Gemini's dialogue designs into Kotlin implementation

#### **Output**: Optimized conversation flows, improved dialogue chains

### Phase 4: Architecture Review & Optimization

#### **Primary Tool: Augment**
- **Tasks**: Deep architectural analysis, performance optimization insights
- **Strategic Queries**:
  - "Analyze the DialogueChain architecture for scalability bottlenecks"
  - "Review the AgentCortex state management pattern for potential improvements"
  - "Assess the voice processing pipeline for optimization opportunities"

#### **Secondary Tool: Claude Code**
- **Tasks**: Implementing Augment's architectural recommendations, conducting preliminary analysis
- **Workflow**: Systematic refactoring based on Augment's insights, detailed code analysis preparation

#### **Output**: Architectural improvements, performance optimizations, comprehensive analysis reports

### Phase 5: Testing & Deployment

#### **Primary Tool: Claude Code**
- **Tasks**: Test execution, build processes, deployment workflows
- **Workflow**:
  1. Run comprehensive test suites
  2. Execute build validation
  3. Manage release processes
  4. Handle git workflows and version tagging

#### **Secondary Tool: Cursor IDE**
- **Tasks**: Test writing, debugging test failures
- **Integration**: Fix issues identified by Claude Code's test execution

#### **Output**: Tested, validated releases

## Enhanced Claude Code Capabilities

### **Advanced Development Tasks**

#### **Codebase Analysis & Documentation**
- **Deep Code Analysis**: Systematic examination of large files (ConversationAgent.kt ~131KB)
- **Architecture Documentation**: Generate comprehensive architectural overviews
- **Dependency Mapping**: Trace complex dependencies across modules
- **Pattern Recognition**: Identify code patterns and anti-patterns
- **Technical Debt Assessment**: Evaluate code quality and maintenance needs

#### **Refactoring Orchestration**
- **Modularization Planning**: Break down large classes into focused modules
- **Safe Refactoring**: Systematic code restructuring with validation
- **Interface Extraction**: Create clean abstractions from existing code
- **Dependency Injection**: Implement and optimize DI patterns
- **Test-Driven Refactoring**: Ensure refactoring doesn't break functionality

#### **Project Management & Coordination**
- **PRD Analysis**: Comprehensive analysis of planning documents
- **Task Prioritization**: Evaluate and sequence development tasks
- **Blocker Resolution**: Identify and resolve development impediments
- **Progress Tracking**: Monitor development milestones and goals
- **Risk Assessment**: Identify potential development risks

#### **Quality Assurance & Validation**
- **Code Review**: Systematic code quality assessment
- **Testing Strategy**: Design comprehensive testing approaches
- **Performance Analysis**: Identify performance bottlenecks
- **Security Review**: Assess code for security vulnerabilities
- **Documentation Validation**: Ensure documentation accuracy

#### **VoxManifestorApp-Specific Tasks**
- **Dialogue Chain Analysis**: Understand complex conversation flows
- **Agent System Optimization**: Improve ConversationAgent architecture
- **Voice Processing Pipeline**: Analyze and optimize voice workflows
- **Core Loop State Management**: Evaluate and improve state transitions
- **Theme Extraction Enhancement**: Optimize AI conversation processing

### **Claude Code as Augment Multiplier**
Given Augment's limited access, Claude Code can serve as a "pre-processor" and "post-processor":

#### **Pre-Augment Preparation**
- **Context Gathering**: Collect relevant code snippets and documentation
- **Problem Formulation**: Clearly define architectural questions
- **Preliminary Analysis**: Provide initial insights to focus Augment queries
- **Data Preparation**: Organize information for optimal Augment consumption

#### **Post-Augment Implementation**
- **Recommendation Implementation**: Systematically apply Augment's insights
- **Validation Testing**: Verify Augment's recommendations work correctly
- **Refinement Iteration**: Adjust implementation based on results
- **Documentation Updates**: Update documentation with architectural changes

## Task-Specific AI Tool Selection

### **Code Implementation**
- **Primary**: Cursor IDE (real-time assistance)
- **Secondary**: Claude Code (systematic operations, refactoring)

### **Architecture & Design**
- **Primary**: Augment (deep analysis)
- **Secondary**: Claude Code (analysis preparation, implementation)
- **Tertiary**: Cursor IDE (implementation)

### **Conversational AI Features**
- **Primary**: Gemini (dialogue design)
- **Secondary**: Claude Code (AI system analysis, integration)
- **Tertiary**: Cursor IDE (implementation)

### **Project Management**
- **Primary**: Claude Code (PRD analysis, task coordination)
- **Secondary**: Cursor IDE (PRD creation)

### **Debugging Complex Issues**
- **Primary**: Claude Code (systematic investigation)
- **Secondary**: Augment (architectural insight)
- **Tertiary**: Cursor IDE (active debugging)

### **Voice Processing & AI Integration**
- **Primary**: Gemini (AI logic design)
- **Secondary**: Claude Code (pipeline analysis, optimization)
- **Tertiary**: Cursor IDE (implementation)

### **Codebase Analysis & Documentation**
- **Primary**: Claude Code (comprehensive analysis)
- **Secondary**: Augment (strategic insights)
- **Tertiary**: Cursor IDE (implementation)

### **Refactoring & Modularization**
- **Primary**: Claude Code (systematic refactoring)
- **Secondary**: Augment (architectural strategy)
- **Tertiary**: Cursor IDE (implementation)

### **Quality Assurance & Testing**
- **Primary**: Claude Code (systematic testing, validation)
- **Secondary**: Cursor IDE (test implementation)

## Daily Development Workflow

### **Morning Strategic Session**
1. **Claude Code**: Conduct comprehensive strategic analysis using daily planning template
   - Analyze current state (scratchpad, PRDs, recent commits)
   - Identify priorities based on strategic objectives
   - Assess blockers and design resolution strategies
   - Create detailed action plan with success criteria
2. **Cursor IDE**: Review/update PRDs based on Claude's strategic analysis
3. **Augment** (if needed): Strategic architectural questions (prepared by Claude)
4. **Claude Code**: Git status, workflow setup, systematic task orchestration

### **Development Session**
1. **Cursor IDE**: Active coding, real-time assistance
2. **Claude Code**: Systematic operations, refactoring, analysis
3. **Gemini** (if needed): Dialogue system optimization
4. **Claude Code**: Quality assurance, validation, testing

### **Evening Progress Assessment**
1. **Claude Code**: Conduct comprehensive progress review using daily planning template
   - Assess completed tasks and outcomes
   - Capture key insights and lessons learned
   - Plan tomorrow's priorities based on progress
   - Recommend strategic adjustments if needed
2. **Claude Code**: Git commits, version management, code quality review
3. **Cursor IDE**: Update documentation based on progress assessment

### **Weekly Analysis Session**
1. **Claude Code**: Comprehensive codebase analysis
2. **Claude Code**: Technical debt assessment
3. **Augment** (strategic use): Architectural optimization review
4. **Claude Code**: Implementation of optimization recommendations

## Strategic Usage Guidelines

### **Augment Optimization** (Limited Access)
- **Best Times**: Early morning for strategic planning
- **Focus Areas**: 
  - Architecture decisions before major refactoring
  - Performance bottleneck analysis
  - Complex debugging insights
- **Avoid**: Simple implementation questions, routine debugging

### **Gemini Optimization** (Limited Access)
- **Best Times**: During dialogue system development sprints
- **Focus Areas**:
  - Conversation flow design
  - Prompt engineering for BrainService
  - Check-in system improvements
- **Avoid**: General coding questions, non-AI features

### **Cursor IDE Maximization** (Unlimited)
- **Use For**: All active development work
- **Strengths**: Real-time context, rapid iteration
- **Integration**: Primary tool for implementing insights from limited-access tools

### **Claude Code Reliability** (Unlimited)
- **Use For**: Systematic operations, workflow management
- **Strengths**: Consistent execution, file management
- **Role**: Development workflow orchestrator

## VoxManifestorApp-Specific Claude Workflows

### **ConversationAgent Modularization**
Based on the scratchpad analysis, Claude Code is ideal for the complex ConversationAgent refactoring:

**Workflow**:
1. **Analysis Phase**: Systematically analyze the 131KB ConversationAgent.kt file
2. **Extraction Planning**: Identify the ~422 lines of concept functions for extraction
3. **Systematic Refactoring**: Create ConceptScreenManager module step-by-step
4. **Validation Testing**: Ensure no breaking changes during modularization
5. **Documentation**: Update architectural documentation

**Why Claude Code**: This requires systematic, methodical work that benefits from Claude's analytical depth and systematic execution.

### **Enhanced Theme Transition Implementation**
Perfect match for Claude's strengths in understanding complex systems:

**Workflow**:
1. **PRD Analysis**: Analyze the enhanced transition requirements from scratchpad
2. **Chain Architecture**: Understand the DialogueChain → TransitionChain flow
3. **Message Enhancement**: Implement detailed theme explanations in Chain B
4. **Integration Testing**: Validate enhanced messages work with existing flows
5. **User Experience**: Ensure detailed messages improve rather than overwhelm

**Why Claude Code**: Complex multi-chain system requiring deep understanding and systematic implementation.

### **Core Loop State Management**
Claude's systematic approach perfect for state machine complexity:

**Workflow**:
1. **State Flow Analysis**: Map the 7-phase Core Loop transitions
2. **AgentCortex Integration**: Analyze state synchronization patterns
3. **Transition Optimization**: Improve state transition reliability
4. **Error Handling**: Implement robust state recovery mechanisms
5. **Performance Monitoring**: Track state transition performance

**Why Claude Code**: Complex state management requiring systematic analysis and careful implementation.

### **Voice Processing Pipeline Optimization**
Leveraging Claude's analytical capabilities for AI system optimization:

**Workflow**:
1. **Pipeline Analysis**: Understand Voice → Google Cloud → AI → Response flow
2. **Bottleneck Identification**: Find performance and reliability issues
3. **Integration Optimization**: Improve BrainService → Google Cloud integration
4. **Error Recovery**: Implement robust voice processing error handling
5. **Testing Framework**: Create comprehensive voice processing tests

**Why Claude Code**: Complex AI pipeline requiring systematic analysis and optimization.

## Tool Synergy Patterns

### **Architecture → Implementation**
1. **Augment**: Analyze architecture, identify improvements
2. **Cursor IDE**: Implement architectural changes
3. **Claude Code**: Manage systematic refactoring

### **Dialogue Design → Integration**
1. **Gemini**: Design conversation flows and prompts
2. **Cursor IDE**: Implement dialogue logic in Kotlin
3. **Claude Code**: Test and deploy conversational features

### **Planning → Execution**
1. **Cursor IDE**: Create PRDs and plans
2. **Claude Code**: Execute systematic development workflow
3. **Cursor IDE**: Active implementation and iteration

## Quality Assurance Integration

### **Code Review Workflow**
1. **Cursor IDE**: Initial code review and suggestions
2. **Augment**: Deep architectural review (when needed)
3. **Claude Code**: Systematic testing and validation

### **Performance Optimization**
1. **Augment**: Identify performance bottlenecks
2. **Cursor IDE**: Implement optimizations
3. **Claude Code**: Validate improvements through testing

## Success Metrics

### **Efficiency Indicators**
- Reduced context switching between tools
- Faster implementation cycles
- Higher quality architectural decisions
- Improved dialogue system performance

### **Quality Indicators**
- Fewer architectural revisions
- Better conversation flow design
- More maintainable code structure
- Reduced debugging time

## Future Considerations

### **Tool Evolution**
- Monitor new AI capabilities and adjust workflows
- Integrate new tools as they become available
- Optimize access patterns based on usage analytics

### **Process Refinement**
- Regular workflow effectiveness reviews
- Adjust tool allocation based on project phase
- Continuous improvement of AI tool synergy

---

This workflow ensures optimal utilization of each AI tool's strengths while maintaining development efficiency and code quality throughout the VoxManifestorApp development lifecycle.