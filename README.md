# VoxManifestor

VoxManifestor is a voice-controlled Android application that helps users manifest their wishes and goals through structured conceptualization and affirmations. The app combines voice interaction, state management, and AI assistance to create an engaging and supportive manifestation experience.

## Core Features

### Voice-Controlled Interface
- Natural voice interaction for all app functions
- Real-time speech recognition using Google Cloud Speech-to-Text
- Voice feedback using Google Cloud Text-to-Speech
- Visual feedback for voice recognition status

### Wish Management
- Store up to 5 active wishes/manifestations
- Voice commands to add, edit, or delete wishes
- Slot-based organization system
- Persistent storage using Room database

### Concept Building
- Break down wishes into Present and Desired states
- Structured concept development with AI guidance
- Visual representation of concept relationships
- Real-time conversation history

### Affirmations
- AI-generated personalized affirmations based on wishes
- Voice-guided affirmation sessions
- Customizable number of affirmations per wish
- Visual overlay for affirmation display



## Technical Architecture

### Key Components
- **ConversationAgent**: Central controller for voice interaction and conversation flow
- **BrainService**: AI integration using Google's Gemini model
- **VoiceManagedViewModel**: Handles voice recognition and synthesis
- **ConceptRepository**: Manages concept storage and retrieval
- **Room Database**: Persistent storage for manifestations and concepts

### State Management
- Reactive UI using Kotlin Flow and StateFlow
- Hierarchical state machine for conversation control
- Real-time status monitoring and logging



## Areas for Improvement

### Voice Recognition
- Enhance noise filtering and accuracy
- Implement better handling of conversation interruptions
- Add support for offline voice recognition

### Concept Building
- Expand concept types beyond Present/Desired states
- Improve AI conversation natural flow
- Concept visualization and relationship mapping

### User Experience
- Add user customization options
- Implement progress tracking
- Enhance visual feedback and animations
- Add support for multiple languages

## Dependencies

- Android SDK 24+
- Google Cloud Speech-to-Text API
- Google Cloud Text-to-Speech API
- Google Gemini AI API
- AndroidX Room
- Kotlin Coroutines
- Jetpack Compose


