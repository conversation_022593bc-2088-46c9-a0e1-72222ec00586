---
description: 
globs: 
alwaysApply: false
---
# Task-Solving Pad Usage Rules

## 1. Purpose

The "Task-Solving Pad" (`.cursor/task_pad.md`) is a lightweight, temporary document used by the EXECUTOR to break down and manage the specific actions required to complete a *single, well-defined task* that has been identified in a PRD or a higher-level planning document (like the main scratchpad). 

Its primary purpose is to serve as a focused working memory for the EXECUTOR during the implementation of one specific task, preventing the main PRD or scratchpad from becoming cluttered with highly granular implementation details.

## 2. When to Use

-   When starting work on a specific, actionable task from a PRD (e.g., "Implement `BrainService` Method for Theme Extraction").
-   When a task requires several sub-steps, code snippets to remember, or a sequence of checks that are too detailed for the PRD itself.

## 3. Lifespan

-   A Task Pad is typically created when the EXECUTOR begins active work on a task.
-   It should be considered temporary. Once the task is fully implemented, tested, and the relevant PRD/scratchpad has been updated with a summary of the task's completion, the Task Pad for that specific task may no longer be needed or can be archived.
-   **It is NOT a replacement for updating the main PRD or scratchpad with task completion summaries.**

## 4. Structure

A Task Pad should be kept simple and focused. It does not require all the formal sections of a PRD. A recommended minimal structure includes:

---
**Task Pad: [Name of the Specific Task from PRD]**

**1. Objective:**
    *   (Copied or summarized from the PRD - what is this specific task trying to achieve?)

**2. Key Files & Modules Involved:**
    *   (List of primary files/classes/modules that will be touched or referenced.)

**3. Implementation Sub-Steps / Checklist:**
    *   (A granular checklist of actions the EXECUTOR will take. This is the core of the Task Pad.)
    *   [ ] Sub-step 1 (e.g., "Define data class X")
    *   [ ] Sub-step 2 (e.g., "Write prompt for Y")
    *   [ ] Sub-step 3 (e.g., "Implement function Z in Class A")
    *   [ ] Sub-step 4 (e.g., "Write unit test for function Z")

**4. Code Snippets & Notes (Working Memory):**
    *   (Temporary place for code ideas, data structures in consideration, API signatures, specific logic notes, etc. This section is highly fluid.)

**5. Blockers / Questions for Planner/User:**
    *   (If any arise specifically during the execution of this task.)

---

## 5. Guidelines for EXECUTOR

-   **IMPORTANT:** Update the checklist as you make progress.  Do not delete tasks once complete. UPDATE THEM.

-   **Process:** Solve one sub-step and check-in with user.  Complete a step at a time.
-   **Focus:** Keep the Task Pad strictly related to the single task at hand.
-   **Brevity:** Be concise. This is a working document, not a formal report.
-   **Clean Up:** Ensure that upon task completion, the main PRD or scratchpad is updated with the overall status.

-   **COMPLETION:** When complete, mark tasks as such and remove implementation details - replace these with a summary of the completed task. Once the task-pad tasks are all complete, we should see a list of briefly described, completed items.


## 6. Relationship to PRDs and Main Scratchpad

-   The Task Pad draws its initial objective from a PRD or the main scratchpad.
-   It provides the detailed "how-to" for a single task listed in those higher-level documents.

