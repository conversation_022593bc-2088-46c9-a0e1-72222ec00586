# Gemini Directives for VoxManifestorApp

## 1. Core Project Philosophy

- **Primary Goal:** Act as a "Trusted Advisor." The app is a voice-driven conversational "genie" that helps users manifest their goals. All interactions and code should reflect this supportive, intelligent, and goal-oriented nature.
- **Core Interface:** The conversation is the primary interface. The agent (`ConversationAgent`) drives the user experience by navigating through a series of conversational contexts defined in the **Core Loop**.

## 2. Source of Truth for Context

- The `/agent/context/` directory is the primary source of truth for all project documentation.
- Always start by consulting the `/agent/context/context_index.md` to find the relevant document for your task. Do not rely on prior knowledge alone.

## 3. Key Architectural & Development Patterns

- **Architecture:** The app follows an **MVVM** pattern with a **Repository** layer. State is managed reactively using **Kotlin Coroutines and `StateFlow`**.
- **Single Source of Truth (SSoT):** `AgentCortex.kt` is the SSoT for all agent-related state. State is mutated by `ConversationAgent` and observed by the UI.
- **Dependency Injection:** Dependencies are managed centrally in `AppContainer.kt`.
- **Error Handling:** The check-in system uses a dual mechanism: `Result` objects at the `BrainService` layer and `try-catch` exception handling at the `ConversationAgent` layer. Emulate this pattern for new features.
- **Modularity:** Strive to keep components decoupled. For example, the `CheckInDialogueChain` handles check-in logic, which is orchestrated, but not implemented, by the `ConversationAgent`.

## 4. Code Contribution Guidelines

- **File Structure:** Adhere to the existing feature-based package structure (e.g., `ui/agent/checkin`, `data`).
- **State Management:** Expose state from ViewModels using `StateFlow`. Do not expose mutable state.
- **Documentation:** When adding or significantly modifying a core feature, update or create corresponding documentation in `/agent/context/`.