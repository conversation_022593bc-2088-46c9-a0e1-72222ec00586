import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.createSavedStateHandle
import androidx.lifecycle.viewmodel.CreationExtras
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.example.voxmanifestorapp.ManifestorApplication
import com.example.voxmanifestorapp.ui.concept.ConceptViewModel
import com.example.voxmanifestorapp.ui.basevox.VoiceManagedViewModel

/**
 * Factory for creating ViewModels across the application.
 * Provides consistent access to dependencies and state management.
 */
object AppViewModelProvider {

    val Factory = viewModelFactory {

        // Initialize VoiceViewModel
        initializer {
            VoiceManagedViewModel(
                permissionState = manifestorApplication().permissionState,
                voiceRecognizer = manifestorApplication().container.voiceRecognitionRepository,
            )
        }

        // Initialize MainViewModel
        initializer {
            MainViewModel(
                repository = manifestorApplication().container.manifestationRepository,
                ttsRepo = manifestorApplication().container.textToSpeechRepository,
                mainScreenState = manifestorApplication().container.mainScreenState,
                agentViewModel = manifestorApplication().container.agentViewModel,
                conversationRepository = manifestorApplication().container.conversationRepository
            )
        }

        // Init ConceptViewModel
        initializer {
            ConceptViewModel(
                conceptRepository = manifestorApplication().container.conceptRepository,
                manifestationRepository = manifestorApplication().container.manifestationRepository,
                agentViewModel = manifestorApplication().container.agentViewModel,
                savedStateHandle = createSavedStateHandle()
            )
        }
    }
}

/**
 * Extension function to get the application instance from CreationExtras.
 */
fun CreationExtras.manifestorApplication(): ManifestorApplication =
    (this[ViewModelProvider.AndroidViewModelFactory.APPLICATION_KEY] as ManifestorApplication)