package com.example.voxmanifestorapp.ui.basevox

import android.media.AudioAttributes
import android.media.AudioFormat
import android.media.AudioTrack
import android.util.Log
import com.example.voxmanifestorapp.data.StatusColor
import com.google.cloud.texttospeech.v1.AudioConfig
import com.google.cloud.texttospeech.v1.AudioEncoding
import com.google.cloud.texttospeech.v1.SynthesisInput
import com.google.cloud.texttospeech.v1.TextToSpeechClient
import com.google.cloud.texttospeech.v1.VoiceSelectionParams
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

interface TextToSpeechRepository {
    suspend fun speak(text: String): Long
    fun release()
    fun setLogger(logStatus: (String, StatusColor) -> Unit)
}

class TextToSpeechGoogleApiRepository(
    private val ttsClient: TextToSpeechClient
) : TextToSpeechRepository {

    private var audioTrack: AudioTrack? = null
    private var statusLogger: (String, StatusColor) -> Unit = { msg, _ -> Log.d(TAG, msg)}

    companion object {
        private const val TAG = "TTSRepository"
        private const val SAMPLE_RATE = 24000
    }

    override fun setLogger(logStatus: (String, StatusColor) -> Unit) {
        this.statusLogger = logStatus
    }

    private fun logStatus(message: String, color: StatusColor = StatusColor.Default) {
        statusLogger(message, color)
    }

    override suspend fun speak(text: String): Long = withContext(Dispatchers.IO) {
        try {
            logStatus("[TTS] Synthesizing speech for: $text")

            // time for a new voice
            val voice = VoiceSelectionParams.newBuilder()
                .setLanguageCode("en-US") // Still specify the language code
                // Set the specific Chirp 3 HD voice name
                .setName("en-US-Chirp3-HD-Zephyr")
                // Do NOT set SsmlGender when specifying a name
                .build()

            /** this was the old and kinda crappyh voice.
            val voice = VoiceSelectionParams.newBuilder()
                .setLanguageCode("en-US")
                .setSsmlGender(com.google.cloud.texttospeech.v1.SsmlVoiceGender.FEMALE)
                .build()
            */

            val audioConfig = AudioConfig.newBuilder()
                .setAudioEncoding(AudioEncoding.LINEAR16)
                .setSampleRateHertz(SAMPLE_RATE)
                .build()

            val input = SynthesisInput.newBuilder()
                .setText(text)
                .build()

            val response = ttsClient.synthesizeSpeech(input, voice, audioConfig)
            val audioData = response.audioContent.toByteArray()

            // calculate duration of utterance
            // .size gives size in bytes.  each sample uses 2 bytes.  * by millis and num samples per sec.
            val durationMs = (audioData.size / 4) * 1000L / SAMPLE_RATE

            val bufferSize = AudioTrack.getMinBufferSize(
                SAMPLE_RATE,
                AudioFormat.CHANNEL_OUT_MONO,
                AudioFormat.ENCODING_PCM_16BIT
            )

            audioTrack = AudioTrack.Builder()
                .setAudioAttributes(
                    AudioAttributes.Builder()
                        .setUsage(AudioAttributes.USAGE_MEDIA)
                        .setContentType(AudioAttributes.CONTENT_TYPE_SPEECH)
                        .build()
                )
                .setAudioFormat(
                    AudioFormat.Builder()
                        .setEncoding(AudioFormat.ENCODING_PCM_16BIT)
                        .setSampleRate(SAMPLE_RATE)
                        .setChannelMask(AudioFormat.CHANNEL_OUT_MONO)
                        .build()
                )
                .setBufferSizeInBytes(bufferSize)
                .build()

            audioTrack?.play()
            audioTrack?.write(audioData, 0, audioData.size)

            logStatus("[TTS] Speech synthesis complete", StatusColor.Pause)

            // returns the function with the duration as a value
            return@withContext durationMs

        } catch (e: Exception) {
            logStatus("[TTS] Error synthesising speech: ${e.message}", StatusColor.Stop)
            throw e
        }
    }

    override fun release() {
        audioTrack?.let { track ->
            try {
                if (track.playState == AudioTrack.PLAYSTATE_PLAYING) {
                    track.stop()
                }
                track.release()
                logStatus("[TTS] AudioTrack released")
            } catch (e: Exception) {
                logStatus("[TTS] Error releasing AudioTrack: ${e.message}", StatusColor.Stop)
            }
        }
        audioTrack = null
    }
}
