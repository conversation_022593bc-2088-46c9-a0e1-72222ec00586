package com.example.voxmanifestorapp.ui.concept

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.example.voxmanifestorapp.data.Concept
import com.example.voxmanifestorapp.data.ConceptItem
import com.example.voxmanifestorapp.data.ConceptType
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import com.example.voxmanifestorapp.ui.basevox.VoiceManagedViewModel
import com.example.voxmanifestorapp.ui.statuslog.StatusMessageService
import com.example.voxmanifestorapp.ui.agent.components.AgentProcessPanel
import com.example.voxmanifestorapp.ui.agent.components.AgentToolbar
import com.example.voxmanifestorapp.ui.agent.components.MinimizedCard
import com.example.voxmanifestorapp.ui.agent.components.ConversationMessage

object ConceptColors {
    // set up colour scheme
    val backgroundColor = Color(0xFFF5E6D3)  // Warm beige from affirmation overlay
    val headerColor = Color(0xFFE6D5B8)      // Slightly darker beige (from affirmation border)
    val wishColor = Color(0xFFE3F2FD)
    val wishBorderColor = Color(0xFFBBDEFB)

    // Main node colors
    val primaryNodeColor = Color(0xFFF9A825)   // For the first/base concept
    val primaryBorderColor = Color(0xFF3F51B5)
    val primaryItemColor = Color(0xFFF8E3B5)    // Light blend for first concept items
    val secondaryNodeColor = Color(0xFF6DAFA9)  // For the second/target concept
    val secondaryItemColor = Color(0xFFFAF4EA)  // Light blend for second concept items
    val secondaryBorderColor = Color(0xFF003631)

    // Common colors
    val itemBorderColor = Color(0xFF3F51B5)      // Indigo border (same as mainscreen)
}

object ConceptShapes {
    val primaryNode = RoundedCornerShape(4.dp)
    val secondaryNode = CircleShape
    val itemShape = RoundedCornerShape(8.dp)
}

private val MonospaceFontFamily = FontFamily.Monospace

@Composable
fun ConceptScreen(
    onNavigateBack: () -> Unit,
    viewModel: ConceptViewModel,
    voiceModel: VoiceManagedViewModel,
    statusMessageService: StatusMessageService,
    onRequestPermission: () -> Unit
) {
    /** collect agent UI state components from agent cortex via viewmodel
     */
    // set up dialog state, conversation history, concepts, status msgs, etc. state collection
    val dialogueState by viewModel.dialogueState.collectAsState()
    val conversationHistory by viewModel.conversationHistory.collectAsState()
    val presentConcept by viewModel.presentConcept.collectAsState()
    val desiredConcept by viewModel.desiredConcept.collectAsState()
    val manifestation by viewModel.manifestation.collectAsState()
    val statusMessages by statusMessageService.statusMessages.collectAsState()
    // recognition state for whether voice recog is on or off (for mic graphic)
    val recognitionState by voiceModel.recognitionState.collectAsState()

    // conversation plan and current action state
    val currentPlan by viewModel.currentPlan.collectAsState()
    val currentAction by viewModel.currentAction.collectAsState()
    val coreLoopState by viewModel.coreLoopState.collectAsState()

    // for minimizing chat history and status bars
    var isChatMinimized by remember { mutableStateOf(true) }
    var isStatusMinimized by remember { mutableStateOf(true) }

    // for the agent interaction screen
    var isAgentExpanded by remember { mutableStateOf(true) }
    var selectedMenuOption by remember { mutableStateOf<String?>(null) }

    // layout vars
    val topAreaHeightEstimate = 70.dp
    val bottomAreaHeightEstimate = 80.dp

    Box(modifier = Modifier.fillMaxSize()) {

        // Layer 1: Main screen content (appears under agent / menu panel)
        Column(
            modifier = Modifier
                .fillMaxSize()
                .systemBarsPadding() // Add padding for system bars
                .background(ConceptColors.backgroundColor)

        ) {        // overall column containing all elements: header, main, voice indicators

            /* header */
            ConceptHeader(onNavigateBack, manifestation?.title ?: "MANIFESTATION NOT FOUND :(")

            Spacer(modifier = Modifier.height(16.dp))

            // begin content area : 60/40 split.
            Column(
                modifier = Modifier
                    .fillMaxSize()
            ) {
                // Concept visualisation area - Scrollable concepts
                Column(
                    modifier = Modifier
                        .fillMaxWidth()

                        // this also determines height of bottom bar, linked to below...
                        .weight(if (isChatMinimized && isStatusMinimized) 0.9f else 0.5f)

                        .padding(horizontal = 8.dp)
                        .verticalScroll(rememberScrollState()),  // Make whole section scrollable
                    verticalArrangement = Arrangement.spacedBy(22.dp)
                ) {
                    ConceptSection(
                        concept = presentConcept,
                        nodeName = "Present State"
                    )
                    ConceptSection(
                        concept = desiredConcept,
                        nodeName = "Desired State"
                    )
                }

                // Monitoring Area
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        // this is the line that determines total height of the bottom bar in proportion to whole screen, linked to above..
                        .weight(if (isChatMinimized && isStatusMinimized) 0.1f else 0.5f)
                        .background(Color(0xFF2D2D2D))
                ) {
                    // Dynamic content area
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f)
                            .padding(horizontal = 8.dp)
                    ) {
                        Column(
                            modifier = Modifier.fillMaxSize(),
                        ) {
                            // Show conversation history if not minimized
                            if (!isChatMinimized) {
                                Card(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(top = 8.dp)
                                        .weight(if (isStatusMinimized) 1f else 0.5f),
                                    colors = CardDefaults.cardColors(
                                        containerColor = Color(
                                            0xFFFFF8E1
                                        )
                                    ),
                                    border = BorderStroke(1.dp, ConceptColors.itemBorderColor)
                                ) {
                                    Box(modifier = Modifier.fillMaxSize()) {
                                        IconButton(
                                            onClick = { isChatMinimized = true },
                                            modifier = Modifier.align(Alignment.TopEnd)
                                        ) {
                                            Icon(Icons.Default.KeyboardArrowDown, "Minimize")
                                        }
                                        LazyColumn(
                                            modifier = Modifier
                                                .fillMaxSize()
                                                .padding(
                                                    top = 40.dp,
                                                    start = 8.dp,
                                                    end = 8.dp,
                                                    bottom = 8.dp
                                                ),
                                            reverseLayout = true
                                        ) {
                                            items(conversationHistory.filter { it.isVisible }
                                                .reversed()) { entry ->
                                                ConversationMessage(entry)
                                            }
                                        }
                                    }
                                }
                            }

                            // Show status log if not minimized
                            if (!isStatusMinimized) {
                                Card(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(top = 8.dp)
                                        .weight(if (isChatMinimized) 1f else 0.5f),
                                    colors = CardDefaults.cardColors(containerColor = Color.Black),
                                    border = BorderStroke(0.dp, ConceptColors.itemBorderColor)
                                ) {
                                    Box(modifier = Modifier.fillMaxSize()) {
                                        IconButton(
                                            onClick = { isStatusMinimized = true },
                                            modifier = Modifier.align(Alignment.TopEnd)
                                        ) {
                                            Icon(
                                                Icons.Default.KeyboardArrowDown,
                                                "Minimize",
                                                tint = Color.White
                                            )
                                        }
                                        val scrollState = rememberScrollState()
                                        LaunchedEffect(statusMessages.size) {
                                            scrollState.animateScrollTo(scrollState.maxValue)
                                        }
                                        Column(
                                            modifier = Modifier
                                                .fillMaxSize()
                                                .padding(top = 40.dp, start = 4.dp, end = 4.dp)
                                                .verticalScroll(scrollState)
                                        ) {
                                            statusMessages.forEach { (text, color) ->
                                                Text(
                                                    text = text,
                                                    style = MaterialTheme.typography.bodyMedium,
                                                    color = color.color,
                                                    modifier = Modifier.padding(vertical = 2.dp)
                                                )
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // Use the shared MinimizedCard component for minimized windows
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(32.dp)
                            .padding(horizontal = 8.dp),
                        horizontalArrangement = Arrangement.SpaceEvenly,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        if (isChatMinimized) {
                            MinimizedCard(
                                title = "Chat",
                                backgroundColor = Color(0xFFFFF8E1),
                                onExpand = { isChatMinimized = false }
                            )
                        }
                        if (isStatusMinimized) {
                            MinimizedCard(
                                title = "Status",
                                backgroundColor = Color.Black,
                                onExpand = { isStatusMinimized = false }
                            )
                        }
                    }

                    // Use the shared AgentToolbar component
                    AgentToolbar(
                        recognitionState = recognitionState,
                        dialogueState = dialogueState,
                        onMicClick = { voiceModel.handleMicrophoneClick(onRequestPermission) },
                        onInterrupt = { viewModel.requestInterrupt() },
                        onToggleConversation = { viewModel.toggleConversation() },
                        onSendResponse = { viewModel.sendResponse() },
                        modifier = Modifier.padding(horizontal = 8.dp)
                    )
                }
                // end monitoring area
            } // end main content area : 60/40
        }   // end main screen content column

        // Use the shared AgentProcessPanel component
        AgentProcessPanel(
            recognitionState = recognitionState,
            dialogueState = dialogueState,
            conversationHistory = conversationHistory,
            currentPlan = currentPlan,
            currentAction = currentAction,
            isExpanded = isAgentExpanded,
            onToggleExpanded = { isAgentExpanded = !isAgentExpanded },
            modifier = Modifier
                .align(Alignment.TopEnd) // Position panel container in the top-right
                .systemBarsPadding()
                .padding(
                    top = topAreaHeightEstimate,      // Push below header/title
                    bottom = bottomAreaHeightEstimate   // Push above bottom bars
                ),
            panelColor = ConceptColors.backgroundColor,
            borderColor = ConceptColors.itemBorderColor,
            coreLoopState = coreLoopState // Pass core loop state
        )
    }
}

@Composable
private fun ConceptHeader(
    onNavigateBack: () -> Unit,
    manifestationTitle: String
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(ConceptColors.headerColor.copy(alpha = 0.7f)) // Slightly darker/different header bg
            .border(BorderStroke(1.dp, ConceptColors.itemBorderColor.copy(alpha = 0.6f)))
            .padding(horizontal = 8.dp, vertical = 4.dp) // Reduced vertical padding
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onNavigateBack, modifier = Modifier.size(36.dp)) { // Smaller button
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "Back",
                    modifier = Modifier.size(24.dp) // Smaller icon
                )
            }
            Text(
                text = "// CONCEPT_VIEW //", // High-tech/retro style title
                style = MaterialTheme.typography.titleSmall, // Smaller title
                fontFamily = MonospaceFontFamily,
                color = ConceptColors.itemBorderColor // Use a theme color
            )
            Spacer(modifier = Modifier.width(36.dp)) // Balance the back button
        }
        // Display Manifestation Title below header, tightly integrated
        Text(
            text = "\"$manifestationTitle \"",
            style = MaterialTheme.typography.bodyMedium,
            fontFamily = MonospaceFontFamily,
            color = Color.DarkGray,
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 4.dp, top = 2.dp, bottom = 4.dp),
            textAlign = TextAlign.Center
        )
    }
}

data class StyleConfig(
    val nodeColor: Color,
    val itemColor: Color,
    val nodeShape: RoundedCornerShape,
    val borderColor: Color
)

@Composable
private fun ConceptSection(
    concept: Concept,
    nodeName: String,
    modifier: Modifier = Modifier
) {
    // todo: this will change when we expand beyond present/desired concepts
    val styleConfig = when(concept.type) {
        is ConceptType.Present -> StyleConfig(
            nodeColor = ConceptColors.primaryNodeColor, itemColor = ConceptColors.primaryItemColor,
            nodeShape = ConceptShapes.primaryNode, borderColor = ConceptColors.primaryBorderColor
        )

        is ConceptType.Desired -> StyleConfig(
            nodeColor = ConceptColors.secondaryNodeColor, itemColor = ConceptColors.secondaryItemColor,
            nodeShape = ConceptShapes.secondaryNode, borderColor = ConceptColors.secondaryBorderColor
        )
        // else branch here should never occur...  at least until we add different concepts.
        else -> StyleConfig(nodeColor = ConceptColors.primaryNodeColor, itemColor = ConceptColors.primaryItemColor, nodeShape = ConceptShapes.primaryNode, borderColor = ConceptColors.primaryBorderColor)
    }
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Concept Node - smaller size
        Surface(
            modifier = Modifier
                .height(60.dp)
                .width(180.dp),
            shape = styleConfig.nodeShape,
            color = styleConfig.nodeColor,
            border = BorderStroke(1.dp, styleConfig.borderColor)
        ) {
            // Concept Node - Refined: Slimmer, simpler, high-tech text style
            Box(
                modifier = Modifier
                    .fillMaxWidth(0.7f) // Take less width
                    .height(30.dp) // Significantly reduced height (was 60.dp)
                    .border(1.dp, styleConfig.borderColor.copy(alpha = 0.8f), RoundedCornerShape(2.dp)) // Thinner, sharper border
                    .padding(horizontal = 8.dp), // Reduced padding
                contentAlignment = Alignment.Center // Center the text
            ) {
                Text(
                    // Add some high-tech flair like brackets or prefix
                    text = "$nodeName",
                    color = styleConfig.borderColor, // Use border color for text for contrast
                    style = MaterialTheme.typography.labelLarge, // Smaller, distinct style
                    fontFamily = MonospaceFontFamily, // Use Monospace
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center
                )
            }
        }

        // Items Column
        Column(
            modifier = Modifier.fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            repeat(concept.type.requiredItems ?: 3) { index ->
                val item = concept.items.getOrNull(index)
                ConceptItem(
                    item = item,
                    backgroundColor = styleConfig.itemColor
                )
            }
        }
    }
}


@Composable
private fun ConceptItem(
    item: ConceptItem?,
    backgroundColor: Color,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .heightIn(min = 48.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(backgroundColor)
            .border(1.dp, ConceptColors.itemBorderColor, RoundedCornerShape(8.dp))
            .padding(12.dp),
        contentAlignment = Alignment.CenterStart
    ) {
        if (item != null) {
            Text(
                text = item.content,
                style = MaterialTheme.typography.bodyMedium
            )
        }
    }
}
