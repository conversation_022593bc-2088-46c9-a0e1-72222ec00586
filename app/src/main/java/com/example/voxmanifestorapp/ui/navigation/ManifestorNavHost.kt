import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.navigation.navArgument
import com.example.voxmanifestorapp.ManifestorApplication
import com.example.voxmanifestorapp.ui.concept.ConceptScreen
import com.example.voxmanifestorapp.ui.concept.ConceptViewModel
import com.example.voxmanifestorapp.ui.basevox.VoiceManagedViewModel
import androidx.compose.runtime.LaunchedEffect

@Composable
fun ManifestorNavHost(
    onRequestPermission: () -> Unit,
) {
    val navController = rememberNavController()
    val container = (LocalContext.current.applicationContext as ManifestorApplication).container
    val voiceModel: VoiceManagedViewModel = viewModel(factory = AppViewModelProvider.Factory)
    
    // set up the voice model for the agent
    val agent = container.conversationAgent

    LaunchedEffect(true) {
        // set status message as main screen for voice model
        voiceModel.setStatusService(container.statusMessageService)
        // send the voice viewmodel to the agent here
        agent.setVoiceManager(voiceModel)
        // send the lambda for concept screen navigation to the agent here
        agent.setConceptNavigation { id ->
            navController.navigate("concept/$id")
        }
        agent.navigationManager.setNavigateBack {
            navController.navigateUp()
        }
    }

    NavHost(navController = navController, startDestination = "main") {
        composable("main") {
            // Get MainViewModel instance
            val mainViewModel: MainViewModel = viewModel(factory = AppViewModelProvider.Factory)
            
            MainScreen(
                onRequestPermission = onRequestPermission,
                viewModel = mainViewModel,
                voiceModel = voiceModel,
                agent = agent,
                statusMessageService = container.statusMessageService,
                onDetailsOpen = { id -> navController.navigate("concept/$id") }
            )
        }

        composable(
            route = "concept/{manifestationId}",
            arguments = listOf(
                navArgument("manifestationId") {
                    type = NavType.IntType
                }
            )
        ) {
            // Get ConceptViewModel instance
            val conceptViewModel: ConceptViewModel = viewModel(factory = AppViewModelProvider.Factory)

            LaunchedEffect(conceptViewModel) { 
                agent.observeConceptViewModel(conceptViewModel)
            }

            ConceptScreen(
                onNavigateBack = { navController.navigateUp() },
                viewModel = conceptViewModel,
                voiceModel = voiceModel,
                statusMessageService = container.statusMessageService,
                onRequestPermission = onRequestPermission,
            )
        }
    }
}
