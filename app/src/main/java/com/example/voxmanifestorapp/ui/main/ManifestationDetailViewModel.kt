import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel

class ManifestationDetailViewModel(
    savedStateHandle: SavedStateHandle,
    private val repository: ManifestationRepository,
) : ViewModel() {

    val manifestationId: Int = savedStateHandle["id"] ?: 0

    //val manifestation: Flow<ManifestationWithDetails> = repository.getManifestationWithDetails(manifestationId)


}
