package com.example.voxmanifestorapp.ui.agent.affirmations

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.example.voxmanifestorapp.ui.agent.DisplayState

@Composable
fun AffirmationOverlay(
    displayState: DisplayState,
    onStop: () -> Unit
) {
    // Semi-transparent background overlay
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0.3f)),
        contentAlignment = Alignment.Center
    ) {
        // Popup card
        Card(
            modifier = Modifier
                .padding(32.dp)
                .width(320.dp)
                .wrapContentHeight(),
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFFF5E6D3)  // Warm beige
            ),
            border = BorderStroke(
                width = 2.dp,
                color = Color(0xFFE6D5B8)  // Slightly darker beige
            ),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Display the appropriate text based on state
                val displayText = when (displayState) {
                    is DisplayState.ShowAffirmation -> {
                        if (displayState.isRepeating) {
                            "Repeat after me:\n${displayState.text}"
                        } else {
                            displayState.text
                        }
                    }
                    is DisplayState.StartSession -> "Starting affirmation session..."
                    is DisplayState.EndSession -> "Session complete"
                    else -> ""
                }

                Text(
                    text = displayText,
                    style = MaterialTheme.typography.headlineSmall,
                    textAlign = TextAlign.Center,
                    color = Color(0xFF4A4033)  // Dark warm gray
                )

                if (displayState is DisplayState.ShowAffirmation && displayState.isRepeating) {
                    Text(
                        text = "${displayState.repeatCount}/2",
                        style = MaterialTheme.typography.titleMedium,
                        color = Color(0xFF7D6E5B)  // Medium warm gray
                    )
                }

                Spacer(modifier = Modifier.height(8.dp))

                Button(
                    onClick = onStop,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFF8B7355)  // Darker warm brown
                    ),
                    modifier = Modifier.height(40.dp)
                ) {
                    Text("Stop")
                }
            }
        }
    }
}