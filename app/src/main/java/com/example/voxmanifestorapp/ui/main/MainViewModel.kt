import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.voxmanifestorapp.data.Manifestation
import com.example.voxmanifestorapp.data.SessionInfo
import com.example.voxmanifestorapp.data.ConversationRepository
import ManifestationRepository
import com.example.voxmanifestorapp.ui.agent.AgentViewModel
import com.example.voxmanifestorapp.ui.agent.ConversationEntry
import com.example.voxmanifestorapp.ui.agent.ConversationPhase
import com.example.voxmanifestorapp.ui.agent.timer.TimerIntent
import com.example.voxmanifestorapp.ui.main.MainScreenState
import com.example.voxmanifestorapp.ui.basevox.TextToSpeechRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch

class MainViewModel(
    private val repository: ManifestationRepository,
    private val ttsRepo: TextToSpeechRepository,
    private val mainScreenState: MainScreenState,
    private val agentViewModel: AgentViewModel,
    private val conversationRepository: ConversationRepository
) : ViewModel() {

    // Use agentViewModel directly
    val dialogueState = agentViewModel.dialogueState
    val commandState = agentViewModel.commandState
    val agentDisplayState = agentViewModel.displayState
    val conversationHistory = agentViewModel.conversationHistory
    val currentPlan = agentViewModel.conversationPlan
    val currentAction = agentViewModel.currentAction
    val coreLoopState = agentViewModel.coreLoopState
    val checkInState = agentViewModel.checkInState
    val conversationType = agentViewModel.conversationType

    // Session information
    private val _sessionInfoList = MutableStateFlow<List<SessionInfo>>(emptyList())
    val sessionInfoList: StateFlow<List<SessionInfo>> = _sessionInfoList.asStateFlow()
    
    // Selected session entries
    private val _sessionEntries = MutableStateFlow<List<ConversationEntry>>(emptyList())
    val sessionEntries: StateFlow<List<ConversationEntry>> = _sessionEntries.asStateFlow()
    
    // Currently viewed session ID (if viewing a past session)
    private val _viewingSessionId = MutableStateFlow<String?>(null)
    val viewingSessionId: StateFlow<String?> = _viewingSessionId.asStateFlow()

    // Take selected slot from main screen state single source of truth
    val selectedSlot = mainScreenState.selectedSlot

    init {
        // Load session info when ViewModel is created
        loadAllSessionInfo()
    }

    /**
     * Loads all session information for display in the session cards
     */
    fun loadAllSessionInfo() {
        viewModelScope.launch {
            _sessionInfoList.value = conversationRepository.getAllSessionsInfo()
        }
    }
    
    /**
     * Loads conversation entries for a specific session
     */
    fun loadSessionEntries(sessionId: String) {
        viewModelScope.launch {
            _viewingSessionId.value = sessionId
            conversationRepository.getSessionEntriesFlow(sessionId)
                .collect { entries ->
                    _sessionEntries.value = entries
                }
        }
    }
    
    /**
     * Returns to viewing the current session
     */
    fun returnToCurrentSession() {
        _viewingSessionId.value = null
    }

    /**
     * Resumes a previous conversation session by loading its context
     */
    fun resumeSession(sessionId: String) {
        Log.d("MainViewModel", "Resuming session: $sessionId")
        
        // Delegate to AgentViewModel for session continuity
        agentViewModel.resumeSession(sessionId)
        
        // Return to current session view
        returnToCurrentSession()
    }

    /**
     * Starts the Core Conversation Loop by submitting a specific intent to the agent.
     * This function is called by the UI to initiate the structured coaching conversation.
     */
    fun startCoreLoop() {
        viewModelScope.launch {
            // Use the specific core loop intent
            agentViewModel.submitCoreLoopIntent()
        }
    }

    // Function to handle selection in the UI
    fun toggleSlotSelection(slot: Int) {
        val currentlySelected = selectedSlot.value
        if (currentlySelected == slot) {
            Log.d("TOGGLE_SLOT", "De-selecting slot ${slot} and interrupting agent.")
            clearSlotSelection()
            requestTerminate()
        } else {
            Log.d("TOGGLE_SLOT", "Selecting slot ${slot}.")
            setSelectedSlot(slot)
        }
    }

    fun setSelectedSlot(slot: Int) {
        mainScreenState.setSelectedSlot(slot)
    }

    fun clearSlotSelection() {
        mainScreenState.clearSelection()
    }

    val allManifestations: Flow<List<Manifestation>> = repository.getAllManifestations()

    fun addManifestation(manifestation: Manifestation) = viewModelScope.launch {
        repository.insertManifestation(manifestation)
    }

    fun deleteManifestation(manifestation: Manifestation) = viewModelScope.launch {
        repository.deleteManifestation(manifestation)
    }

    // Delegate agent control functions directly
    fun requestTerminate() = agentViewModel.requestTerminate()
    fun requestInterrupt() = agentViewModel.requestInterrupt()

    fun toggleConversation() = agentViewModel.toggleConversation()

    fun sendResponse() = agentViewModel.sendResponse()

    /**
     * Handles timer control intents from the UI
     */
    fun handleTimerIntent(intent: TimerIntent) {
        viewModelScope.launch {
            agentViewModel.handleTimerIntent(intent)
        }
    }
}
