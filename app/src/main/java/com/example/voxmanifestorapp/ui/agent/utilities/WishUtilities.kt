package com.example.voxmanifestorapp.ui.agent.utilities

import ManifestationRepository
import android.util.Log
import com.example.voxmanifestorapp.data.ConceptRepository
import com.example.voxmanifestorapp.data.ConceptType
import com.example.voxmanifestorapp.data.MAX_WISH_SLOTS
import com.example.voxmanifestorapp.data.Manifestation
import com.example.voxmanifestorapp.data.StatusColor
import kotlinx.coroutines.flow.first
import kotlinx.serialization.Serializable

/**
 * Summary of a user's wish, intended for providing context to the agent
 */
@Serializable
data class WishSummary(
    val id: Int,
    val title: String
)

/**
 * Enhanced wish summary including present and desired state items for better theme correlation
 */
@Serializable
data class EnhancedWishSummary(
    val id: Int,
    val title: String,
    val presentStateItems: List<String> = emptyList(),  // Content of present state items
    val desiredStateItems: List<String> = emptyList()   // Content of desired state items
)



/**
 * Fetches the user's active wishes to provide context for the check-in dialogue.
 * Limits to a maximum of 5 wishes for prompt efficiency.
 *
 * @param repository ManifestationRepository for accessing wish data
 * @param logger Optional logging function for status messages
 * @return List of WishSummary objects representing the user's active wishes
 */
suspend fun fetchUserWishesForContext(
    repository: ManifestationRepository,
    logger: ((String, StatusColor) -> Unit)? = null
): List<WishSummary> {
    logger?.invoke("📋 WISHES: Fetching user wishes for context", StatusColor.Default)

    return try {
        val manifestations = repository.getAllManifestations().first()

        if (manifestations.isEmpty()) {
            logger?.invoke("📋 WISHES: No wishes found", StatusColor.Default)
            emptyList()
        } else {
            val wishSummaries = manifestations
                .sortedBy { it.slot }
                .take(5)
                .map { manifestation ->
                    WishSummary(
                        id = manifestation.id,
                        title = manifestation.title
                    )
                }
            logger?.invoke(
                "📋 WISHES: Found ${wishSummaries.size} wishes for context",
                StatusColor.Default
            )
            wishSummaries
        }
    } catch (e: Exception) {
        logger?.invoke("📋 WISHES: Error fetching wishes: ${e.message}", StatusColor.Stop)
        emptyList()
    }
}

/**
 * Fetches enhanced wish data including present and desired state items for transition processing.
 * This provides richer context for theme-to-wish correlation in the TransitionChain.
 *
 * @param manifestationRepository Repository for accessing manifestation data
 * @param conceptRepository Repository for accessing concept data
 * @param logger Optional logging function for status messages
 * @return List of EnhancedWishSummary objects with concept items included
 */
suspend fun fetchEnhancedWishDataForTransition(
    manifestationRepository: ManifestationRepository,
    conceptRepository: ConceptRepository,
    logger: ((String, StatusColor) -> Unit)? = null
): List<EnhancedWishSummary> {
    logger?.invoke("📋 ENHANCED WISHES: Fetching enhanced wish data for transition", StatusColor.Default)

    return try {
        val manifestations = manifestationRepository.getAllManifestations().first()

        if (manifestations.isEmpty()) {
            logger?.invoke("📋 ENHANCED WISHES: No wishes found", StatusColor.Default)
            emptyList()
        } else {
            val enhancedWishes = manifestations
                .sortedBy { it.slot }
                .take(5) // Limit for prompt efficiency
                .map { manifestation ->
                    // Fetch present state items
                    val presentStateItems = try {
                        val presentConcept = conceptRepository.getConceptWithItems(
                            manifestation.id,
                            ConceptType.Present
                        ).first()
                        presentConcept?.items?.map { it.content } ?: emptyList()
                    } catch (e: Exception) {
                        logger?.invoke("📋 ENHANCED WISHES: Error fetching present state for wish ${manifestation.id}: ${e.message}", StatusColor.Default)
                        emptyList()
                    }

                    // Fetch desired state items
                    val desiredStateItems = try {
                        val desiredConcept = conceptRepository.getConceptWithItems(
                            manifestation.id,
                            ConceptType.Desired
                        ).first()
                        desiredConcept?.items?.map { it.content } ?: emptyList()
                    } catch (e: Exception) {
                        logger?.invoke("📋 ENHANCED WISHES: Error fetching desired state for wish ${manifestation.id}: ${e.message}", StatusColor.Default)
                        emptyList()
                    }

                    EnhancedWishSummary(
                        id = manifestation.id,
                        title = manifestation.title,
                        presentStateItems = presentStateItems,
                        desiredStateItems = desiredStateItems
                    )
                }

            logger?.invoke(
                "📋 ENHANCED WISHES: Found ${enhancedWishes.size} enhanced wishes for transition",
                StatusColor.Default
            )
            enhancedWishes
        }
    } catch (e: Exception) {
        logger?.invoke("📋 ENHANCED WISHES: Error fetching enhanced wishes: ${e.message}", StatusColor.Stop)
        emptyList()
    }
}


/**
 * Represents the status of a wish for prioritization purposes.
 * This is a transient computational result used to determine the next focus area.
 */
data class WishStatus(
    val slot: Int,
    val manifestationId: Int?,  // Store ID instead of entity
    val isDefinedOnMainScreen: Boolean,
    val hasCompletePresentState: Boolean,
    val hasCompleteDesiredState: Boolean,
    val lastDiscussedTimestamp: Long?
) {
    /**
     * Returns true if the wish has both Present and Desired states completed
     */
    val isConceptDefinitionComplete: Boolean
        get() = hasCompletePresentState && hasCompleteDesiredState
}

/**
 * Contains logic for selecting the next wish to focus on in the Core Conversation Loop.
 * The prioritization logic follows this order:
 * 1. Find the first slot without a defined Manifestation
 * 2. If all defined, find the first Manifestation with incomplete concepts
 * 3. If all complete, find the Manifestation least recently discussed
 */
class WishPriorityManager {
    
    companion object {
        private const val TAG = "WishPriority"
        private var lastSelectionReason = ""
        
        /**
         * Returns the reason why the last wish was selected
         */
        fun getLastSelectionReason(): String {
            return lastSelectionReason
        }
        
        /**
         * Selects the next wish to focus on based on prioritization rules
         * 
         * @param manifestationRepository Repository for accessing Manifestation data
         * @param conceptRepository Repository for accessing Concept data
         * @return The slot index of the next wish to focus on (0-4)
         */
        suspend fun selectNextWish(
            manifestationRepository: ManifestationRepository,
            conceptRepository: ConceptRepository
        ): Int {
            Log.d(TAG, "Selecting next wish...")
            
            // Get all manifestations
            val manifestations = manifestationRepository.getAllManifestations().first()
            
            // Build status for all slots
            val wishStatusList = buildWishStatusList(manifestations, conceptRepository)
            
            // Debug log the status of each wish
            wishStatusList.forEach { status ->
                Log.d(TAG, "Slot ${status.slot}: " +
                        "defined=${status.isDefinedOnMainScreen}, " +
                        "presentComplete=${status.hasCompletePresentState}, " +
                        "desiredComplete=${status.hasCompleteDesiredState}, " +
                        "lastDiscussed=${status.lastDiscussedTimestamp}")
            }
            
            // PRIORITY 1: Find first empty slot
            val emptySlot = wishStatusList.find { !it.isDefinedOnMainScreen }
            if (emptySlot != null) {
                Log.d(TAG, "Selected empty slot ${emptySlot.slot}")
                lastSelectionReason = "empty_slot"
                return emptySlot.slot
            }
            
            // PRIORITY 2: Find first with incomplete concepts
            val incompleteConceptsWish = wishStatusList.find { !it.isConceptDefinitionComplete }
            if (incompleteConceptsWish != null) {
                if (!incompleteConceptsWish.hasCompletePresentState) {
                    lastSelectionReason = "missing_present_state"
                } else {
                    lastSelectionReason = "missing_desired_state"
                }
                Log.d(TAG, "Selected slot ${incompleteConceptsWish.slot} with incomplete concepts")
                return incompleteConceptsWish.slot
            }
            
            // PRIORITY 3: Find least recently discussed (oldest timestamp or null)
            val oldestDiscussed = wishStatusList.minByOrNull { it.lastDiscussedTimestamp ?: 0L }
            if (oldestDiscussed != null) {
                Log.d(TAG, "Selected least recently discussed slot ${oldestDiscussed.slot}")
                lastSelectionReason = "least_recently_updated"
                return oldestDiscussed.slot
            }
            
            // Fallback - should never reach here as there's always at least 1 slot
            Log.d(TAG, "Fallback to slot 0")
            lastSelectionReason = ""
            return 0
        }
        
        /**
         * Builds a list of WishStatus objects for all slots
         */
        private suspend fun buildWishStatusList(
            manifestations: List<Manifestation>,
            conceptRepository: ConceptRepository
        ): List<WishStatus> {
            val result = mutableListOf<WishStatus>()
            
            // Check each slot
            for (slot in 0 until MAX_WISH_SLOTS) {
                val manifestation = manifestations.find { it.slot == slot }
                
                // If this slot has a manifestation, check its concept status
                val hasCompletePresentState = if (manifestation != null) {
                    val presentConcept = conceptRepository.getConceptWithItems(
                        manifestation.id, 
                        ConceptType.Present
                    ).first()
                    
                    // Check if it has the required number of items
                    presentConcept?.items?.size ?: 0 >= (ConceptType.Present.requiredItems ?: 0)
                } else false
                
                val hasCompleteDesiredState = if (manifestation != null) {
                    val desiredConcept = conceptRepository.getConceptWithItems(
                        manifestation.id,
                        ConceptType.Desired
                    ).first()
                    
                    // Check if it has the required number of items
                    desiredConcept?.items?.size ?: 0 >= (ConceptType.Desired.requiredItems ?: 0)
                } else false
                
                result.add(
                    WishStatus(
                        slot = slot,
                        manifestationId = manifestation?.id,
                        isDefinedOnMainScreen = manifestation != null,
                        hasCompletePresentState = hasCompletePresentState,
                        hasCompleteDesiredState = hasCompleteDesiredState,
                        lastDiscussedTimestamp = manifestation?.lastDiscussedTimestamp
                    )
                )
            }
            
            return result
        }
    }
}
