package com.example.voxmanifestorapp.ui.agent.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandIn
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkOut
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.example.voxmanifestorapp.R
import com.example.voxmanifestorapp.data.RecognitionState
import com.example.voxmanifestorapp.ui.agent.DialogueState
import com.example.voxmanifestorapp.ui.basevox.SpeechIndicatorLights

@Composable
fun AgentToolbar(
    recognitionState: RecognitionState,
    dialogueState: DialogueState,
    onMicClick: () -> Unit,
    onInterrupt: () -> Unit,
    onToggleConversation: () -> Unit,
    onSendResponse: () -> Unit,
    modifier: Modifier = Modifier
) {
    // check if the user is allowed to send a response to the brain...
    val canSendResponse = dialogueState is DialogueState.ExpectingInput

    Row(
        modifier = modifier
            .fillMaxWidth()
            .height(56.dp)
            .padding(bottom = 0.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Left side - Control buttons
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // Mic button
            MicButton(
                recognitionState = recognitionState,
                onMicClick = onMicClick
            )

            // Interrupt brain speech button
            InterruptButton(
                onInterrupt = onInterrupt,
                dialogueState = dialogueState
            )

            // Toggle conversation button
            IconButton(
                onClick = onToggleConversation,
                modifier = Modifier.size(48.dp)
            ) {
                Icon(
                    painter = painterResource(
                        id = if (dialogueState != DialogueState.Idle)
                            R.drawable.stop
                        else
                            R.drawable.go
                    ),
                    contentDescription = if (dialogueState != DialogueState.Idle)
                        "End Conversation"
                    else
                        "Start Conversation",
                    tint = if (dialogueState != DialogueState.Idle) Color.Red else Color.Green,
                    modifier = Modifier.size(24.dp)
                )
            }

            // Send response button - now with proper enabled state
            IconButton(
                onClick = onSendResponse,
                enabled = canSendResponse,
                modifier = Modifier.size(48.dp)
            ) {
                // Use background color to provide stronger visual cue for enabled state
                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(if (canSendResponse) Color(0x1A4CAF50) else Color.Transparent)
                        .padding(8.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.speech),
                        contentDescription = "Send Response",
                        tint = if (canSendResponse) Color.Green else Color.Gray,
                        modifier = Modifier.size(24.dp)
                    )
                }
            }
        }

        // Right side - Speech indicators
        Box(
            modifier = Modifier.wrapContentWidth(),
            contentAlignment = Alignment.Center
        ) {
            SpeechIndicatorLights(dialogueState)
        }
    }
}

@Composable
fun MicButton(
    recognitionState: RecognitionState,
    onMicClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    IconButton(
        onClick = onMicClick,
        modifier = modifier.size(48.dp)
    ) {
        Image(
            painter = painterResource(
                id = when (recognitionState) {
                    RecognitionState.Listen_Active -> R.drawable.mic_on
                    else -> R.drawable.mic_off
                }
            ),
            contentDescription = if (recognitionState == RecognitionState.Listen_Active)
                "Microphone On" else "Microphone Off",
            modifier = Modifier.size(40.dp)
        )
    }
}

@Composable
fun InterruptButton(
    onInterrupt: () -> Unit,
    dialogueState: DialogueState,
    modifier: Modifier = Modifier
) {
    // Check if agent is speaking
    val isSpeaking = dialogueState is DialogueState.Speaking

    // The button is always visible, but we'll animate its appearance when speaking
    Box {
        // Base button (always visible but grayed out when not speaking)
        IconButton(
            onClick = { if (isSpeaking) onInterrupt() },
            modifier = modifier
                .size(48.dp)
                .alpha(if (isSpeaking) 0f else 0.5f) // Hidden when speaking, grayed when not
        ) {
            Icon(
                imageVector = Icons.Default.Close,
                contentDescription = "Stop Speaking (Inactive)",
                tint = Color.Gray,
                modifier = Modifier.size(24.dp)
            )
        }

        // Animated button (only visible when speaking)
        AnimatedVisibility(
            visible = isSpeaking,
            enter = fadeIn() + expandIn(),
            exit = fadeOut() + shrinkOut()
        ) {
            IconButton(
                onClick = onInterrupt,
                modifier = modifier
                    .size(48.dp)
                    .background(Color(0x22FF0000), shape = CircleShape)
            ) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "Stop Speaking",
                    tint = Color.Red,
                    modifier = Modifier.size(24.dp)
                )
            }
        }
    }
}