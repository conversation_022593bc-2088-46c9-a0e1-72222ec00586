package com.example.voxmanifestorapp.ui.basevox

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.example.voxmanifestorapp.ui.agent.DialogueState

@Composable
fun SpeechIndicatorLights(dialogueState: DialogueState?) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(48.dp),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Light indicators
        Box(
            modifier = Modifier
                .size(32.dp)
                .background(
                    when (dialogueState) {
                        is DialogueState.Speaking -> Color.Red
                        else -> Color.Red.copy(alpha = 0.3f)
                    },
                    shape = CircleShape
                )
        )

        Spacer(modifier = Modifier.width(16.dp))

        Box(
            modifier = Modifier
                .size(32.dp)
                .background(
                    when (dialogueState) {
                        is DialogueState.ExpectingInput -> Color.Green
                        else -> Color.Green.copy(alpha = 0.3f)
                    },
                    shape = CircleShape
                )
        )

        Spacer(modifier = Modifier.width(8.dp))

    }
}