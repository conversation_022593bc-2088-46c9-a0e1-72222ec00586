package com.example.voxmanifestorapp.ui.agent.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowLeft
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowLeft
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import com.example.voxmanifestorapp.data.SessionInfo
import com.example.voxmanifestorapp.data.StatusColor
import com.example.voxmanifestorapp.ui.agent.ConversationEntry

/**
 * Status panel component that slides in/out from the right side of the screen
 * with a vertical tab that allows toggling its visibility
 */
@Composable
fun StatusPanel(
    statusMessages: List<Pair<String, StatusColor>>,
    isExpanded: Boolean,
    onToggleExpanded: () -> Unit,
    modifier: Modifier = Modifier,
    height: Dp = 400.dp, // Fixed height of 400dp
    tabPosition: Float = 0.5f // Position tab along the panel height (0.0f-1.0f)
) {
    val panelWidth = 400.dp  // Panel width
    val tabWidth = 25.dp  // Increased from 20.dp to 30.dp (50% increase)
    val tabHeight = 80.dp
    val cornerRadius = 16.dp
    val borderWidth = 0.dp  // Remove border
    val panelShape = RoundedCornerShape(topStart = cornerRadius, bottomStart = cornerRadius)
    val panelColor = Color.Black.copy(alpha = 0.9f)
    val borderColor = Color(0xFF3F51B5)

    BoxWithConstraints(
        modifier = modifier
            .height(height)
            .width(panelWidth)
            .zIndex(10f)
    ) {
        val boxHeight = this.maxHeight
        
        // The main slidable panel content
        AnimatedVisibility(
            visible = isExpanded,
            enter = slideInHorizontally(
                initialOffsetX = { it }, // Slide in from right
                animationSpec = tween(300)
            ),
            exit = slideOutHorizontally(
                targetOffsetX = { it }, // Slide out to right
                animationSpec = tween(300)
            ),
            modifier = Modifier.matchParentSize()
        ) {
            Card(
                modifier = Modifier
                    .fillMaxSize(),
                shape = panelShape,
                colors = CardDefaults.cardColors(containerColor = panelColor)
            ) {
                val scrollState = rememberScrollState()
                LaunchedEffect(statusMessages.size) {
                    scrollState.animateScrollTo(scrollState.maxValue)
                }
                
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp)
                        .verticalScroll(scrollState)
                ) {
                    Text(
                        "System Status",
                        style = MaterialTheme.typography.titleMedium.copy(color = Color.White),
                        modifier = Modifier
                            .padding(bottom = 8.dp)
                            .align(alignment = Alignment.CenterHorizontally)
                    )
                    statusMessages.forEach { (text, color) ->
                        Text(
                            text = text,
                            style = MaterialTheme.typography.bodyMedium,
                            color = color.color,
                            modifier = Modifier.padding(vertical = 2.dp)
                        )
                    }
                }
            }
        }

        // Calculate vertical position based on the tabPosition parameter
        val yOffset = (boxHeight * tabPosition) - (tabHeight / 2)
        
        // Tab to toggle expansion - with custom positioning
        Card(
            modifier = Modifier
                .align(Alignment.CenterEnd)
                .offset(x = 0.dp, y = yOffset)
                .width(tabWidth)
                .height(tabHeight)
                .clickable { onToggleExpanded() },
            shape = RoundedCornerShape(topStart = cornerRadius, bottomStart = cornerRadius), // Flat side on right
            colors = CardDefaults.cardColors(containerColor = panelColor)
        ) {
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier.fillMaxSize()
            ) {
                Icon(
                    imageVector = if (isExpanded) 
                        Icons.Default.KeyboardArrowRight 
                    else 
                        Icons.Default.KeyboardArrowLeft,
                    contentDescription = if (isExpanded) "Collapse Status" else "Expand Status",
                    tint = Color.White
                )
            }
        }
    }
}

/**
 * Conversation history panel component that slides in/out from the left side of the screen
 * with a vertical tab that allows toggling its visibility.
 * Displays the conversation between the agent and user.
 * 
 * The enhanced version shows expandable session cards for historical conversations.
 */
@Composable
fun ConversationHistoryPanel(
    conversationHistory: List<ConversationEntry>,
    isExpanded: Boolean,
    onToggleExpanded: () -> Unit,
    modifier: Modifier = Modifier,
    panelWidth: Dp = 400.dp,
    panelColor: Color = Color(0xFF394A59), // Default to match the toolbar dark slate blue
    tabPosition: Float = 0.5f, // Position tab 50% down from the top (center) by default
    contentPadding: PaddingValues = PaddingValues(16.dp),
    title: String = "Conversation History",
    titleStyle: TextStyle = MaterialTheme.typography.titleMedium.copy(color = Color.White),
    heightFraction: Float = 0.4f, // Take up 50% of screen height by default
    sessionInfoList: List<SessionInfo> = emptyList(),
    onSessionSelected: (String) -> Unit = {},
    onResumeSession: (String) -> Unit = {}
) {
    // Define panel properties
    val tabWidth = 25.dp // Increased from 20.dp to 30.dp (50% increase)
    val tabHeight = 80.dp
    val cornerRadius = 16.dp
    val panelShape = RoundedCornerShape(topEnd = cornerRadius, bottomEnd = cornerRadius)
    
    // Calculate screen height using LocalConfiguration
    val screenHeight = LocalConfiguration.current.screenHeightDp.dp
    val panelHeight = screenHeight * heightFraction
    
    BoxWithConstraints(
        modifier = modifier
            .height(panelHeight)
            .width(panelWidth)
            .zIndex(10f)
    ) {
        val boxHeight = this.maxHeight
        
        // The main slidable panel content
        AnimatedVisibility(
            visible = isExpanded,
            enter = slideInHorizontally(
                initialOffsetX = { fullWidth -> -fullWidth },
                animationSpec = tween(durationMillis = 300)
            ),
            exit = slideOutHorizontally(
                targetOffsetX = { fullWidth -> -fullWidth },
                animationSpec = tween(durationMillis = 300)
            ),
            modifier = Modifier.matchParentSize()
        ) {
            Card(
                modifier = Modifier
                    .fillMaxSize(),
                shape = panelShape,
                colors = CardDefaults.cardColors(containerColor = panelColor)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(contentPadding)
                ) {
                    Text(
                        title,
                        style = titleStyle,
                        modifier = Modifier
                            .padding(bottom = 8.dp)
                            .align(alignment = Alignment.CenterHorizontally)
                    )
                    
                    if (sessionInfoList.isEmpty()) {
                        // Default view - show current conversation only
                        LazyColumn(
                            modifier = Modifier
                                .fillMaxWidth()
                                .weight(1f),
                            reverseLayout = true
                        ) {
                            items(conversationHistory.filter { it.isVisible }.reversed()) { entry ->
                                ConversationMessage(entry)
                            }
                        }
                    } else {
                        // Enhanced view with session cards
                        LazyColumn(
                            modifier = Modifier
                                .fillMaxWidth()
                                .weight(1f)
                        ) {
                            // Add current session at the top if there are messages
                            if (conversationHistory.isNotEmpty()) {
                                item {
                                    Text(
                                        "Current Session",
                                        style = MaterialTheme.typography.labelLarge.copy(color = Color.White),
                                        modifier = Modifier.padding(vertical = 8.dp)
                                    )
                                    LazyColumn(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .height(200.dp) // Fixed height for current session
                                            .padding(bottom = 16.dp),
                                        reverseLayout = true
                                    ) {
                                        items(conversationHistory.filter { it.isVisible }.reversed()) { entry ->
                                            ConversationMessage(entry)
                                        }
                                    }
                                    Divider(color = Color.White.copy(alpha = 0.2f))
                                    Text(
                                        "Previous Sessions",
                                        style = MaterialTheme.typography.labelLarge.copy(color = Color.White),
                                        modifier = Modifier.padding(vertical = 8.dp)
                                    )
                                }
                            }
                            
                            // Add previous session cards
                            items(sessionInfoList.filter { !it.isCurrentSession }) { sessionInfo ->
                                SessionCard(
                                    sessionInfo = sessionInfo,
                                    onSessionSelected = onSessionSelected,
                                    onResumeSession = onResumeSession
                                )
                            }
                        }
                    }
                }
            }
        }

        // Calculate vertical position based on the tabPosition parameter
        // This places the tab at the specified position along the panel height
        val yOffset = (boxHeight * tabPosition) - (tabHeight / 2)

        // Tab to toggle expansion with custom positioning based on constraints
        Card(
            modifier = Modifier
                .offset(x = 0.dp, y = yOffset)
                .width(tabWidth)
                .height(tabHeight)
                .clickable { onToggleExpanded() },
            shape = RoundedCornerShape(topEnd = cornerRadius, bottomEnd = cornerRadius), // Flat side on left
            colors = CardDefaults.cardColors(containerColor = panelColor)
        ) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                val icon = if (isExpanded) 
                    Icons.AutoMirrored.Filled.KeyboardArrowLeft 
                else 
                    Icons.AutoMirrored.Filled.KeyboardArrowRight
                
                Icon(
                    imageVector = icon,
                    contentDescription = if (isExpanded) "Hide Conversation" else "Show Conversation",
                    tint = Color.White,
                    modifier = Modifier.padding(start = 2.dp)
                )
            }
        }
    }
}

/**
 * Displays a single session card with metadata and expandable conversation content.
 */
@Composable
fun SessionCard(
    sessionInfo: SessionInfo,
    onSessionSelected: (String) -> Unit,
    onResumeSession: (String) -> Unit = {},
    modifier: Modifier = Modifier
) {
    var isExpanded by remember { mutableStateOf(false) }
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF263238) // Darker shade for the cards
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(8.dp)
        ) {
            // Session header (always visible)
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { isExpanded = !isExpanded },
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    // Show session name if available, otherwise show date/time
                    Text(
                        text = sessionInfo.sessionName ?: sessionInfo.dateTimeFormatted,
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color.White
                    )
                    // Show date/time as subtitle if session name is available
                    if (sessionInfo.sessionName != null) {
                        Text(
                            text = sessionInfo.dateTimeFormatted,
                            style = MaterialTheme.typography.bodySmall,
                            color = Color.White.copy(alpha = 0.7f)
                        )
                    }
                    Text(
                        text = "${sessionInfo.entryCount} messages",
                        style = MaterialTheme.typography.bodySmall,
                        color = Color.White.copy(alpha = 0.7f)
                    )
                }
                
                Icon(
                    imageVector = if (isExpanded) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown,
                    contentDescription = if (isExpanded) "Collapse" else "Expand",
                    tint = Color.White
                )
            }
            
            // Expandable content
            AnimatedVisibility(visible = isExpanded) {
                Column(modifier = Modifier.padding(top = 8.dp)) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Button(
                            onClick = { onResumeSession(sessionInfo.sessionId) },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFF4CAF50) // Green button for resume
                            )
                        ) {
                            Text("Resume")
                        }
                        
                        Button(
                            onClick = { onSessionSelected(sessionInfo.sessionId) },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFF2196F3) // Blue button for view
                            )
                        ) {
                            Text("View Conversation")
                        }
                    }
                }
            }
        }
    }
}