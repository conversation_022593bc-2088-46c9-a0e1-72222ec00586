import androidx.compose.foundation.layout.*
import androidx.compose.material.*
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

/*
@Composable
fun ManifestationDetailScreen(viewModel: ManifestationDetailViewModel) {
    var selectedTab by remember { mutableStateOf(0) }
    val tabs = listOf("Details", "Ecology", "Present/Desired")

    Column {
        TabRow(selectedTabIndex = selectedTab) {
            tabs.forEachIndexed { index, title ->
                Tab(
                    text = { Text(title) },
                    selected = selectedTab == index,
                    onClick = { selectedTab = index }
                )
            }
        }
        when (selectedTab) {
            0 -> ManifestationDetailsTab(viewModel)
            1 -> EcologyCheckTab(viewModel)
            2 -> PresentDesiredStateTab(viewModel)
        }
    }
}


@Composable
fun ManifestationDetailsTab(viewModel: ManifestationDetailViewModel) {
    val manifestation by viewModel.manifestation.collectAsState()

    Column(modifier = Modifier.padding(16.dp)) {
        EditableList(
            title = "Sensory Outcomes",
            items = manifestation.sensoryOutcomes,
            onItemChanged = viewModel::updateSensoryOutcome
        )
        EditableList(
            title = "Evidence",
            items = manifestation.evidences,
            onItemChanged = viewModel::updateEvidence
        )
        EditableList(
            title = "Resources",
            items = manifestation.resources,
            onItemChanged = viewModel::updateResource
        )
    }
}

@Composable
fun EcologyCheckTab(viewModel: ManifestationDetailViewModel) {
    val manifestation by viewModel.manifestation.collectAsState()

    Column(modifier = Modifier.padding(16.dp)) {
        EditableList(
            title = "Intentions",
            items = manifestation.ecologyChecks.filter { it.type == "intention" },
            onItemChanged = { index, value -> viewModel.updateEcologyCheck(index, "intention", value) }
        )
        EditableList(
            title = "Outcomes",
            items = manifestation.ecologyChecks.filter { it.type == "outcome" },
            onItemChanged = { index, value -> viewModel.updateEcologyCheck(index, "outcome", value) }
        )
        EditableList(
            title = "Consequences",
            items = manifestation.ecologyChecks.filter { it.type == "consequence" },
            onItemChanged = { index, value -> viewModel.updateEcologyCheck(index, "consequence", value) }
        )
    }
}

@Composable
fun PresentDesiredStateTab(viewModel: ManifestationDetailViewModel) {
    val manifestation by viewModel.manifestation.collectAsState()

    Column(modifier = Modifier.padding(16.dp)) {
        EditableList(
            title = "Present State",
            items = manifestation.presentStates,
            onItemChanged = viewModel::updatePresentState
        )
        EditableList(
            title = "Desired State",
            items = manifestation.desiredStates,
            onItemChanged = viewModel::updateDesiredState
        )
        // Placeholder for LLM-generated scenario
        Text("LLM-generated scenario will appear here")
    }
}

@Composable
fun EditableList(
    title: String,
    items: List<ListItem>,
    onItemChanged: (Int, String) -> Unit
) {
    Column {
        Text(title, style = MaterialTheme.typography.h6)
        Spacer(modifier = Modifier.height(8.dp))
        items.forEachIndexed { index, item ->
            TextField(
                value = item.description,
                onValueChange = { onItemChanged(index, it) },
                modifier = Modifier.fillMaxWidth()
            )
            Spacer(modifier = Modifier.height(8.dp))
        }
        Button(onClick = { onItemChanged(items.size, "") }) {
            Text("Add Item")
        }
    }
}
*/

