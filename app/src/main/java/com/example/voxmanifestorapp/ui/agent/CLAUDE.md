# ConversationAgent: The Central Hub

*This document details the architecture and responsibilities of the `ConversationAgent`, the central controller for all conversational operations in VoxManifestor. For high-level project goals see [project_context.md](project_context.md), and for details on specific sub-systems see [core_loop_context.md](core_loop_context.md) and [checkin_context.md](checkin_context.md).*

## 1. Overview & Core Responsibilities

The `ConversationAgent` is the "brain" of the VoxManifestor app. It acts as the primary orchestrator, sitting at the intersection of user interface, state management, and AI services. It is a long-lived class, instantiated once, that manages the entire lifecycle of user interaction.

Its key responsibilities include:
- **Conversation Lifecycle Management**: Initiating, managing, and terminating all conversational flows.
- **State Orchestration**: Acting as the primary interactor with `AgentCortex` to read and update application state.
- **Input/Output Control**: Interfacing with `VoiceManagedViewModel` to manage voice recognition (STT) and `TextToSpeechRepository` for spoken output (TTS).
- **Logic & Flow Control**: Determining the sequence of conversational steps based on the current `ConversationType` and `DialogueState`.
- **AI Service Integration**: Making calls to `BrainService` to leverage LLM intelligence for generating responses, making decisions, and using tools.
- **Sub-system Delegation**: Orchestrating and delegating tasks to specialized modules like `CheckInDialogueChain` for the `CHECK_IN` phase.

## 2. Architecture & Key Interactions

The `ConversationAgent` does not work in isolation. It is deeply integrated with several key components:

- **`AgentCortex`**: The agent's "nervous system." The `ConversationAgent` is the primary mutator of state within the Cortex. It sends intents to update state (`dialogueState`, `coreLoopState`, `conversationHistory`, etc.) and observes state changes to react accordingly. This separation ensures a clean, unidirectional data flow.

- **`VoiceManagedViewModel` & `TextToSpeechRepository`**: These are the agent's "mouth and ears." The agent instructs them when to start/stop listening and what to speak. It receives raw text from the recognition engine and processes it.

- **`BrainService`**: The agent's connection to the LLM. It formats prompts, sends requests for various tasks (e.g., Core Loop guidance, concept building decisions), and processes the LLM's responses.

- **UI Layers (`MainScreen`, `ConceptScreen`)**: The agent indirectly influences the UI via state changes in `AgentCortex`. It also receives intents from the UI (e.g., `RequestToggleConversation`) and can be given references to ViewModels (like `ConceptViewModel`) to orchestrate screen-specific logic.

## 3. Architecture Diagram

This diagram illustrates the `ConversationAgent`'s central role as an orchestrator, managing the flow of data and control between the user interface, state management, and various backend services and sub-systems.

```
+------------------+        +------------------+
|      USER        |        |     AGENT        |
|                  |        |     SYSTEMS      |
| +-------------+  |        | +-------------+  |
| |   UI Layers  |<---------+| AgentCortex  |  |
| | Main/Concept |  |        | |  (State)   |  |
| +-------------+  |        | +------^------+  |
|        ^         |        |        |         |
|        |         |        |        |         |
| +------+------+  |        | +------+------+  |
| | Voice Input |  |        | |Conversation |  |
| |    (Mic)    |----------->|    Agent    |  |
| +-------------+  |        | +------^------+  |
|                  |        |        |         |
| +-------------+  |        | +------+------+  |
| | Audio Output|<---------+| TTS Repo     |  |
| |  (Speaker)  |  |        | |             |  |
| +-------------+  |        | +-------------+  |
+------------------+        +------------------+
          |                           |
          |                           |
+------------------+        +------------------+
|   AI & DATA      |        |    AGENT         |
|                  |        |    SUB-SYSTEMS   |
| +-------------+  |        | +-------------+  |
| | BrainService|<---------+|DialogueChain |  |
| |   (LLM)     |  |        | |             |  |
| +-------------+  |        | +-------------+  |
|                  |        |                  |
| +-------------+  |        | +-------------+  |
| |Repositories |<---------+|HistoryToken  |  |
| |Wish/Concept |  |        | |  Manager    |  |
| +-------------+  |        | +-------------+  |
|                  |        |                  |
|                  |        | +-------------+  |
|                  |        | |Tool Libraries| |
|                  |        | |             |  |
|                  |        | +-------------+  |
+------------------+        +------------------+
```

## 4. Operational Paradigms

The `ConversationAgent` operates under two distinct paradigms, which determine how it interprets user input and manages the conversation. The transition between these modes is fundamental to the app's user experience.

For a detailed breakdown of the specific states involved (`DialogueState`, `ConversationType`, etc.), see **[`conversation_states_and_ui.md`](conversation_states_and_ui.md)**.

### 4.1. Idle / Command-Listening Paradigm
This is the agent's default state. It is not engaged in an extended, multi-turn conversation.
- **`ConversationType`**: `null`
- **`DialogueState`**: `Idle`
- **Behavior**: The agent actively listens for specific keywords (e.g., "start", "select", "read") defined in `VoiceCommandEntity.kt`. These commands trigger discrete actions or initiate structured, non-LLM-driven flows (like `WishCollection`).

### 4.2. Active Conversation Paradigm
This mode is for extended, stateful dialogues.
- **`ConversationType`**: A non-null value (e.g., `CoreLoop`, `ConceptBuilding`).
- **`DialogueState`**: Typically `Speaking`, `Thinking`, or `ExpectingInput`.
- **Behavior**: Most keywords are ignored (except for "stop"). User input is treated as content for the ongoing dialogue, which is managed either by a simple state machine (for legacy flows) or, more importantly, by the LLM via `BrainService` (for `CoreLoop` and `ConceptBuilding`).

## 5. Conversation Lifecycle Management

The agent manages a robust conversation lifecycle, controlled primarily through `toggleBrainConversation()`.

- **`conversationScope`**: A crucial `CoroutineScope` that is created for each main conversation (`CoreLoop`, `ConceptBuilding`). Cancelling this scope instantly and cleanly stops all ongoing processes for that conversation, including LLM calls and delays.
- **`ConversationType`**: An enum (`CoreLoop`, `ConceptBuilding`, `WishSelection`, etc.) that defines the overall context and logic for the current interaction.
- **`DialogueState`**: An enum (`Idle`, `Speaking`, `Thinking`, `ExpectingInput`) that manages the micro-level state of the back-and-forth conversation, ensuring the agent doesn't listen to itself or process input at the wrong time.
- **Toggling Conversations**: The `toggleBrainConversation()` function is the master switch. It checks if a `conversationScope` is active. If so, it gracefully terminates the conversation. If not, it initiates a new one based on the current UI context (e.g., `CoreLoop` on `MainScreen`).

## 6. Core Loop & Legacy Flow Orchestration

### 6.1. The Modern `CoreLoop`
The agent is the direct implementer of the conceptual `CoreLoop`.
- It calls `initiateCoreLoopProcess()` to set up the initial state.
- It uses `progressCoreLoop()` to advance through the phases (`ConversationPhase`).
- For the `CHECK_IN` phase, it delegates control to the `CheckInDialogueChain` by calling `progressCheckIn()`.
- For other phases, it constructs prompts, gets guidance from the `BrainService`, and speaks responses directly.

### 6.2. Legacy `ConceptBuilding` Flow
The agent contains the original `initiateConceptBuilding()` logic, which runs a separate, LLM-driven conversation on the Concept screen.
- **Integration Goal**: This functionality will be migrated into the main `CoreLoop` as dedicated phases (`PRESENT_STATE_EXPLORATION`, `DESIRED_STATE_EXPLORATION`, `CONTRAST_ANALYSIS`). This will create a unified conversational experience where users can seamlessly transition from a general check-in to detailed work on a specific wish.

### 6.3. Legacy Command-Driven Flows
The agent also manages the older, non-LLM `WishCollection` and `WishSelection` flows, which are triggered by simple voice commands. These are simple state machines that will eventually be replaced by more intelligent `CoreLoop` phases.

## 7. Sub-Systems & Utilities

The `ConversationAgent` relies on several specialized utilities to keep its own logic clean and focused.

- **`HistoryTokenManager`**: Before including conversation history in a prompt, the agent uses this utility to ensure the history fits within the LLM's token limit. It intelligently truncates the history from the beginning while preserving the most recent turns, and adds a system note indicating that truncation occurred.

- **`HistoryFormatter`**: This utility is used to convert the `List<ConversationEntry>` from the conversation history into a formatted, human-readable string suitable for an LLM prompt. It handles speaker roles and provides a consistent format across all prompt-building functions.

- **Tool Libraries (`AffirmationTool`, `ConceptToolLibrary`)**: For specific, complex tasks, the agent uses dedicated tool classes. For example, the `AffirmationTool` encapsulates all the logic for generating and executing an affirmation sequence. This keeps the agent's main body from being cluttered with task-specific implementation details.

## 8. Error Handling

The agent features a centralized `handleError(error: Throwable)` function to manage exceptions, particularly from `BrainService` calls.
- It distinguishes between `NetworkException`, which triggers a full conversation teardown and a user-facing error message, and other exceptions.
- This centralized approach ensures consistent error handling and prevents crashes, providing a more resilient user experience.

## 9. Proposed Structural Improvements

The current `ConversationAgent` implementation has evolved organically over time, resulting in a monolithic class (3,000+ lines) that handles multiple distinct responsibilities. This section outlines a proposed refactoring strategy to improve maintainability, testability, and extensibility.

### 9.1. Current Challenges

- **God Object Anti-pattern**: The agent acts as a "catch-all" class, combining input processing, state management, business logic, error handling, and UI coordination.
- **Intertwined Paradigms**: The "Idle/Command-Listening" mode and "Active Conversation" mode are interwoven, making the control flow difficult to follow.
- **Legacy and Modern Systems**: The class contains both the legacy state-machine driven conversations (`WishCollection`, `WishSelection`) and the newer, LLM-driven flows (`CoreLoop`, `ConceptBuilding`).
- **Limited Testability**: The size and coupling make unit testing challenging, as individual responsibilities cannot be easily isolated.

### 9.2. Proposed Architecture

A more maintainable architecture would separate the agent into:

```
ConversationAgent (Orchestrator)
├── CommandProcessor
│   ├── handleSelectCommand()
│   ├── handleReadCommand()
│   └── ...
├── CoreLoopManager
│   ├── progressCoreLoop()
│   ├── handleCheckIn()
│   └── ...
├── ConceptScreenManager
│   ├── initiateConceptBuilding()
│   ├── getNextBrainDecision()
│   └── ...
└── ConversationHelper
    ├── speak()
    ├── handleError()
    └── ...
```

**`ConversationAgent`**: A lean orchestrator that receives inputs and delegates to the appropriate manager based on the current state. It would hold references to the other components and manage the high-level conversation state.

**`CommandProcessor`**: Responsible for handling all keyword-based commands from the "Idle" paradigm. This includes functions like `handleSelectCommand()`, `handleDefineCommand()`, `handleReadCommand()`, etc.

**`CoreLoopManager`**: Manages the entire lifecycle of the `CoreLoop`, including phase progression, interaction with `CheckInDialogueChain`, and eventually, the logic from `ConceptBuilding`. It would own functions like `progressCoreLoop()` and `handleTransition()`.

**`ConceptScreenManager`**: Rather than being "legacy" code, this is actually a parallel sub-loop conversational process that manages interactions on the Concept Screen. It contains the conversation logic for concept exploration including functions like `initiateConceptBuilding()` and `getNextBrainDecision()`. Eventually, these capabilities should be integrated into the core loop as dedicated phases.

**`ConversationHelper`**: Contains utility functions shared across multiple components, like `speak()`, `handleError()`, etc., reducing duplication.

### 9.3. Implementation Strategy

1. **Start with Dependency Injection**: Refactor the agent to accept its dependencies (services, repositories) through constructor parameters rather than direct instantiation.

2. **Extract Helper Functions**: Move utility functions like `speak()`, `handleError()`, `addToHistory()` to a separate helper class.

3. **Isolate `CommandProcessor`**: Extract command-handling logic from the agent and test it independently.

4. **Containerize `CoreLoopManager`**: Move all `CoreLoop` related functions into a dedicated manager, ensuring it can function independently.

5. **Quarantine Legacy Code**: Move the legacy state machine flows into `ConceptScreenManager` to isolate them from the rest of the codebase.

6. **Slim Down `ConversationAgent`**: Reduce the main agent class to a thin orchestration layer that primarily:
   - Receives inputs (from UI or voice)
   - Manages high-level state transitions
   - Delegates processing to the appropriate manager

This modular approach provides clear boundaries for future extensions and makes it easier to integrate the `ConceptBuilding` flow directly into the `CoreLoop` as dedicated phases. 

## 10. Check-In Integration

The Check-In process is a critical component of the `ConversationAgent`'s functionality, serving as the first phase of the Core Loop. It uses a sophisticated multi-chain dialogue system to welcome users, facilitate reflection, and determine when to transition to the next phase.

### 10.1. CheckInDialogueChain Architecture

The `CheckInDialogueChain` implements a 4-chain architecture for sophisticated dialogue processing:

1. **Chain 1: Transition Evaluation** - Determines if it's time to move to the next phase
2. **Chain 1B: Theme Extraction** - Identifies themes in the user's message
3. **Chain 2: Strategy Selection** - Chooses the most appropriate conversation strategy
4. **Chain 3: Response Generation** - Crafts the agent's response incorporating themes

The `ConversationAgent` interacts with this system through the following key methods:

- `progressCheckIn()` - The primary orchestration method that manages the entire check-in flow
- `progressCoreLoop()` - Monitors check-in completion and transitions to the next phase
- `handleThemeBasedTransition()` - Processes the transition decision from the dialogue chain

For a detailed breakdown of the Check-In architecture, including conversation strategies, error handling, and data flow, see the dedicated **[`checkin_context.md`](checkin_context.md)** file.

### 10.2. Check-In Data Flow

```
ConversationAgent.progressCheckIn()
    ↓
Create ConversationContext snapshot
    ↓
CheckInDialogueChain.processTurn()
    ↓
Receive DialogueChainResponse (text, strategy, themes, transition)
    ↓
Update themes and metrics in AgentCortex
    ↓
Speak response or handle transition to next phase
```

### 10.3. Integration Points

- **State Management**: Check-in state is maintained in `AgentCortex.checkInState` and includes tracking of conversation themes and engagement metrics
- **Error Handling**: Exceptions from the dialogue chain are caught and processed by the `ConversationAgent`'s centralized error handling system
- **Theme Persistence**: Themes extracted during check-in can provide valuable context for later phases of the Core Loop