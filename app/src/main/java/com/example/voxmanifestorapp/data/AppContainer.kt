package com.example.voxmanifestorapp.data

import ManifestationRepository
import android.content.Context
import androidx.room.Room
import com.example.voxmanifestorapp.R
import com.example.voxmanifestorapp.ui.agent.AgentCortex
import com.example.voxmanifestorapp.ui.agent.AgentViewModel
import com.example.voxmanifestorapp.ui.agent.BrainService
import com.example.voxmanifestorapp.ui.agent.ConversationAgent
import com.example.voxmanifestorapp.ui.basevox.HOSTNAME
import com.example.voxmanifestorapp.ui.basevox.PORT
import com.example.voxmanifestorapp.ui.basevox.TextToSpeechGoogleApiRepository
import com.example.voxmanifestorapp.ui.basevox.TextToSpeechRepository
import com.example.voxmanifestorapp.ui.basevox.VoiceRecognitionGoogleApiRepository
import com.example.voxmanifestorapp.ui.basevox.VoiceRecognitionRepository
import com.example.voxmanifestorapp.ui.main.MainScreenState
import com.example.voxmanifestorapp.ui.statuslog.StatusMessageService
import com.example.voxmanifestorapp.ui.utils.SoundPlayer
import com.google.ai.client.generativeai.BuildConfig
import com.google.api.gax.core.FixedCredentialsProvider
import com.google.auth.oauth2.ServiceAccountCredentials
import com.google.cloud.speech.v1.SpeechClient
import com.google.cloud.speech.v1.stub.GrpcSpeechStub
import com.google.cloud.speech.v1.stub.SpeechStubSettings
import com.google.cloud.texttospeech.v1.TextToSpeechClient
import com.google.cloud.texttospeech.v1.TextToSpeechSettings
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob

// sets up an interface for the app container, mainly for human usage
interface AppContainer {
    val database: ManifestationDatabase
    val manifestationRepository: ManifestationRepository
    val speechClient: SpeechClient
    val voiceRecognitionRepository: VoiceRecognitionRepository
    val ttsClient: TextToSpeechClient
    val textToSpeechRepository: TextToSpeechRepository
    val statusMessageService: StatusMessageService
    val brainService: BrainService
    val conceptRepository: ConceptRepository
    val conversationAgent: ConversationAgent
    val conversationRepository: ConversationRepository
    // the following screen states serve as APIs between the main screen and the agent, so the agent retains control and oversight
    // and can observe changes in the main screen and respond accordingly
    val mainScreenState: MainScreenState
    val agentCortex: AgentCortex
    val soundPlayer: SoundPlayer
    val agentViewModel: AgentViewModel

    // relegated for now :
    //val userPreferencesRepository: UserPreferencesRepository
}

class AppDataContainer(private val context: Context): AppContainer {

    // overrides the value declared in the interface above
    override val database: ManifestationDatabase by lazy {
        Room.databaseBuilder(
            context.applicationContext,
            ManifestationDatabase::class.java,
            "manifestation-database"
        )
        .addMigrations(
            ManifestationDatabase.MIGRATION_1_2, ManifestationDatabase.MIGRATION_2_3,
            ManifestationDatabase.MIGRATION_3_4, ManifestationDatabase.MIGRATION_4_5,
            ManifestationDatabase.MIGRATION_5_6, ManifestationDatabase.MIGRATION_6_7
        )
        .build()
    }

    override val manifestationRepository: ManifestationRepository by lazy {
        ManifestationRepository(database.manifestationDao())
    }

    override val conceptRepository: ConceptRepository by lazy {
        ConceptRepository(database.conceptDao())
    }


    override val speechClient: SpeechClient by lazy {
        val credentials = context.resources.openRawResource(R.raw.creds).let {
            ServiceAccountCredentials.fromStream(it)
        }

        val stubSettings = SpeechStubSettings.newBuilder()?.apply {
            credentialsProvider = FixedCredentialsProvider.create(credentials)
            endpoint = "$HOSTNAME:$PORT"
        }?.build()

        var grpcStub = GrpcSpeechStub.create(stubSettings)
        SpeechClient.create(grpcStub)
    }

    override val voiceRecognitionRepository: VoiceRecognitionRepository by lazy {
        VoiceRecognitionGoogleApiRepository(speechClient, context)
    }

    override val ttsClient: TextToSpeechClient by lazy {
        val credentials = context.resources.openRawResource(R.raw.creds).let {
            ServiceAccountCredentials.fromStream(it)
        }

        val settings = TextToSpeechSettings.newBuilder()
            .setCredentialsProvider(FixedCredentialsProvider.create(credentials))
            .build()

        TextToSpeechClient.create(settings)
    }

    override val textToSpeechRepository: TextToSpeechRepository by lazy {
        TextToSpeechGoogleApiRepository(ttsClient)
    }

    override val statusMessageService: StatusMessageService by lazy {
        StatusMessageService()
    }

    override val brainService: BrainService by lazy {
        BrainService(com.example.voxmanifestorapp.BuildConfig.GEMINI_API_KEY)
    }

    /** todo: not sure if these are in use any more?
     */
    override val mainScreenState: MainScreenState by lazy {
        MainScreenState()
    }
    override val agentCortex: AgentCortex by lazy {
        AgentCortex()
    }

    override val soundPlayer: SoundPlayer by lazy {
        SoundPlayer(context)
    }

    // Create AgentViewModel instance here, using other container dependencies
    override val agentViewModel: AgentViewModel by lazy {
        AgentViewModel(
            agentCortex = agentCortex,
            soundPlayer = soundPlayer,
            conversationRepository = conversationRepository,
            conversationAgent = conversationAgent
        )
    }

    override val conversationAgent : ConversationAgent by lazy {
        ConversationAgent(
            repository = manifestationRepository,
            ttsRepo = textToSpeechRepository,
            brainService = brainService,
            statusService = statusMessageService,
            scope = CoroutineScope(Dispatchers.Default + SupervisorJob()),
            conceptRepository = conceptRepository,
            mainScreenState = mainScreenState,
            agentCortex = agentCortex,
            conversationRepository = conversationRepository
        )
    }

    override val conversationRepository: ConversationRepository by lazy {
        ConversationRepository(database.conversationLogDao())
    }

/*    override val userPreferencesRepository: UserPreferencesRepository by lazy {
        UserPreferencesRepository(context)
    }

 */

}
