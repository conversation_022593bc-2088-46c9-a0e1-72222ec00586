# The Core Loop

I created my Vox Manifestor app*. to help users manifest their wishes through structured reflection practices. The app centers around a voice-controlled, Ai powered Agent (that I call "**<PERSON><PERSON>") whose job is to guide users through a process of articulating, refining, and affirming their wishes: To facilitate processes that ultimately MANIFEST WHAT THEY DESIRE*

The Genie's goals are:

    Hold a conversation for as long as the user uses the app. (maintain the "Core-Loop")

    Be goal-oriented, and achieve multiple, staged, and progressively deeper goals through these conversations, including:

        Eliciting and storing the user's 5 most valued desires (This <PERSON>ie grants 5 "Wishes"!)

        Gathering daily meta-goal information about, for example, how the user feels about their progress towards these, and to get the user to engage at a deeper level with each of them in turn.

        Use questioning skills to establish detailed descriptions of the present and desired states for each of these wishes.

            Determine sticking points and highlight these in the framework

            Explore detailed explorations of the present state and visualisations of desired states

    Determine when using an affirmation tool could be helpful, and determine from past conversations, what content those affirmations should have!


## CORE LOOP CONCEPT

    The conversation with the A.i. must be THE PRIMARY INTERFACE.

    The A.i. must be able to navigate a sequence of evolving CONVERSATION CONTEXTS, each of which could involve different goals, skills and draw on different knowledge and memories.

    The agent must maintain a sense of consistency (from which the user infers a degree of competence), within each session, and across sessions (it must have a Short term Memory and a Long term Memory), and it must be able to use these memories effectively to keep the conversation relevant and goal-directed!

    UI elements should be CONTEXTUAL and appear only when relevant to the ongoing conversation.

    Different data sources (wishes, states, etc.) must be loaded based on conversation context

    All Navigation must happen through conversation. The agent progresses the context, persists across screen changes, and maintains control of the conversation.**

    The interface maintains a clean, focused design centered on the agent avatar and the conversation

    Information like wish lists, concept details, process and status information become available in collapsible & expandable panels: This "Interface of the Future" must be Dynamic, Fluid and Adaptable.

## The Conversation Is The Primary Interface

At its core this means that the conversational agent must always have somewhere to go in the conversation. The conversation must be structured, and the agent must navigate through this structure, creating a sense of systematic progress for the user.

On the one hand, the agent must not suffer from such issues as repeating the same question more than once or getting stuck on a particular outcome or using a particular process repeatedly. There must always be forward momentum, and the sense that the agent has direction. Otherwise the user can quickly lose faith in the integrity of the interface. At the same time the agent should be able to respond to the user's needs, for example, to focus on a specific wish, when they are expressed.

## The Agent Navigates Conversational Contexts

This follows on naturally from the process oriented nature of the conversation: 

A process oriented conversation necessarily involves moving sequentially between a pre-planned series of sub-conversations.

The goal of the "Core Loop" is to navigate this sequence from beginning to end, repeatedly, ad infinitum (with variations that keep the process interesting, entertaining, engaging, etc.)

The goal of each individual conversational context or sub-conversation will be different according to that context.

The agent will need to maintain awareness of the Core Loop, while being able to descend a logical level into more detailed Conversation Contexts, accomplish the goal of that context to its satisfaction, and then return to the higher level Core Loop, to continue the process.

## Core Loop Architecture: Loops Within Loops

Understanding the hierarchical structure is essential:

1. **The Core Loop** - The highest level conversation cycle that encompasses the entire user journey.
   
2. **Sub-Loops** - Specialized conversation contexts within the Core Loop, each with its own purpose:
   - **Check-In Sub-Loop** (Phase 1) - The entry point that leads to all other phases
   - **Wish Collection Sub-Loop** (Phase 2)
   - **Present State Exploration Sub-Loop** (Phase 3)
   - And so on...

Each sub-loop maintains its own internal state, progression logic, and success criteria, while remaining coordinated with the overall Core Loop flow.

The two main processes contained inside the basic Core Loop are (1) to engage the user in a general daily update on the main screen and (2) to drill into any of the specific goals.

Imagine the Core Loop as the main engine of the coaching agent. Its fundamental purpose is to proactively and continuously guide the user through a journey of exploring, defining, and refining all of their main wishes over time.

## CORE-LOOP: A recurring coaching cycle

    * It Checks In: It starts by figuring out where the user is now and what needs attention.

    * It Focuses: It helps select one specific wish to work on during the current interaction.

    * It Guides Exploration: For that chosen wish, it leads the user through different phases or topics of reflection (like understanding the present situation, clarifying the desired future, analyzing the gap, maybe later finding pathways or dealing with blocks).

    * It Remembers & Adapts: It uses past interactions to inform the current conversation, ensuring continuity and avoiding repetition.

    * It Cycles: Once the relevant exploration for one wish in this session feels complete, the loop decides whether to check in again, move to another wish, or conclude the session. It's designed to run repeatedly whenever the user engages, gradually deepening the understanding of each wish.

    * It Prioritizes: Over time, it tries to ensure all wishes get attention, perhaps focusing on those least recently discussed or those that seem incomplete.

*** Essentially, the Core Loop is the persistent, guiding intelligence that ensures the user is always moving forward in clarifying and working towards their set of manifestations. *** 

## INITIALIZE: The Daily Check-In

This is the starting gate or the daily briefing phase within the Core Loop. It happens each time the user starts a new session with the agent (or potentially when cycling back after working deeply on one wish).

### The Check-In Flow: A Sub-Loop with Internal Progression

The Check-In phase operates as its own complete conversational sub-loop:

1. **OPENING Stage**: This is where the agent engages in free-form conversation:
   - Welcomes the user back
   - Facilitates voice journaling and reflection
   - Extracts themes silently in the background
   - Uses various conversation strategies based on user engagement
   - Monitors for transition triggers (direct requests, engagement patterns)

2. **TRANSITION Stage**: Only activated when the agent determines it's time to move on:
   - Presents a summary of extracted conversation themes
   - Asks the user which theme feels most important to focus on
   - Captures the user's selection
   - Creates rich context for the next phase
   - Only then signals to the Core Loop to progress to the next phase

This separation of OPENING and TRANSITION stages ensures the Check-In serves both as a valuable reflection opportunity and a pathway to more structured work. Each stage has its own goals, but together they form a coherent sub-loop within the larger Core Loop architecture.

## CHECK-IN: Creating The Dialogue Sequence

    Re-engage & Orient: Welcome the user back and set the stage for the session.

    Create Continuity: Briefly reference what was discussed last time (e.g., "Last session we focused on your 'Better Job' wish..."). This makes the interaction feel connected.

    General Check-in / Active Journaling: Ask broad, open-ended questions about how the user is feeling today regarding their goals or life in general ("What's been on your mind?", "How are you feeling about your progress lately?"). This allows the user space to voice current concerns or successes before diving deep. The agent might mirror back what it hears ("So it sounds like you're feeling...") to encourage reflection.

    Collaborative Focus Selection: Guide the user towards choosing one specific wish to work on during this session. This involves asking clarifying "Meta-Goal Questions" (like "Which goal feels most important right now?", "Are you feeling stuck anywhere?") if a focus doesn't emerge naturally from the check-in.

    Obtain User Consent: Once a potential focus wish is identified (either by the user or suggested by the agent based on the check-in or priority), the agent must explicitly ask for the user's agreement ("Okay, it sounds like X is top of mind. Shall we explore that further today?").

    Transition: Only after the user agrees to focus on a specific wish does this Initialize Process phase complete. The Core Loop then transitions into the next appropriate phase (like exploring the Present State) for that specific, user-approved wish. If the user doesn't want to focus on the suggested wish, the agent stays in this phase and continues the selection process ("Okay, no problem. Is there another wish you'd prefer to look at?").

### Main Goals of Core Loop

This is the crucial onboarding step for each session within the Core Loop. It ensures continuity, allows for voice journaling, and collaboratively sets the focus with the user's consent before the deeper, wish-specific work begins.

We need to construct a series of lines of questioning for the user that will get them talking about their current situation, their feelings, and whatever else they need to get out of their head as part of a brain-dump and assisted-journalling situation.

Then, after an (as-yet undefined) period of time we would expect the Core Loop to bring the user towards discussing specific goals.

So this Daily Check-In would start with the general situational context of things happening in the user's environment and mental / emotional processes they are currently going through, and start to gradually focus in on more immediate and specific priorities, gradually moving them from the present state to the desired future.

This initial phase is potentially useful for two main reasons:

    It gives the user time to reflect on what is currently happening in their world and in their brains, providing a sounding board and some perspective by being able to get certain issues, problems and thoughts out of their heads. This has value more generally for the wish-specific component, as it brings to mind the things that the user wants to move away from, and how they don't want things to be.

    It provides a potentially rich source of information about the user's mental state, barriers to goals, inner monologues and limiting emotional patterns, so that a (admittedly more sophisticated) agent in the future could craft specific affirmations or belief-change / reframing processes based on this data.

We now have some important questions to answer:

    What is the "ideal" conversation flow?

    How does the agent decide whether to ask clarifying, "go-deeper" questions or mirroring techniques?

    How many investigative questions should the agent ask during the daily check-in?
        What form should these questions take?

    How do we decide how many of these questions are asked?
        Should the agent try to engage the user with these questions for a minimum amount of time / or try to gauge the user's engagement with the conversation and respond to that?

## Practical Solutions

    Metric-Based Progression

    * Use quantifiable metrics (like question counts) to track progress through a conversation. An increase in user self-disclosure and a shift from broad questions to specific goal-setting would tend to indicate the conversation is moving forward appropriately. Metrics can trigger state transitions, e.g.

    Ask at least 2 questions from each of 3 main questioning categories

    Monitor answer length for each; if response length above a threshold, ask more questions until response length drops, or until we reach an upper limit of questions asked.

    External State Management: We combine the usage of data structures within our agent, as well as algorithms that feed this data to the LLM at the right time. LLMs themselves lack an innate concept of our desired questioning stages beyond what's in the prompt, so an external mechanism must be used to manage state.

    Linear Sequence with Enforced Transitions: A predetermined sequence of stages and use an external state variable or memory to track which stage is active. So the agent should enforce phase transitions only when requirements are met.

    Structured Response Format: Our idea to have the LLM return categorized metadata about its responses aligns with the "invisible thinking vs. speaking" approach, where "the model could generate a hidden rationale in JSON (for the agent to parse) and a final message for the user."

## Refinements to Our Implementation

For a detailed breakdown of the `ConversationAgent` architecture that implements these concepts, including its state management, tool usage, and interaction with the `BrainService`, please see **[`convo_agent_context.md`](convo_agent_context.md)**.
