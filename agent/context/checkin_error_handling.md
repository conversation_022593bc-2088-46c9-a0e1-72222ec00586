# Check-In System Error Handling

This document details the error handling architecture for the VoxManifestor Check-In system. It describes how errors flow through the system, from their origin point in external API calls to their ultimate handling in the user interface.

## System Architecture: Simplified Error Propagation

The VoxManifestor Check-In system employs a nuanced, multi-layered error handling strategy. This involves different error reporting mechanisms at different architectural layers, with `ConversationAgent` now using direct exception handling for calls to `Dialogue<PERSON>hain`.

```
┌────────────────────┐     ┌────────────────────┐     ┌────────────────────┐
│                    │     │                    │     │                    │
│  ConversationAgent │     │    DialogueChain   │     │    BrainService    │
│                    │     │                    │     │                    │
│  ┌───────────────┐ │     │ ┌───────────────┐  │     │ ┌───────────────┐  │
│  │progressCheckIn│ │     │ │  processTurn  │  │     │ │getCheckInEval.│  │
│  └───────┬───────┘ │     │ └───────┬───────┘  │     │ └───────┬───────┘  │
│          │         │     │         │          │     │         │          │
│          │ call    │     │         │ call     │     │         │          │
│          ▼         │     │         ▼          │     │         ▼          │
│  ┌───────────────┐ │     │ ┌───────────────┐  │     │ ┌───────────────┐  │
│  │ try {         │ │     │ │evaluateTransit│  │     │ │  Gemini API   │  │
│  │  catch (e) {  │ │     │ └───────┬───────┘  │     │ └───────┬───────┘  │
│  │   handleError │ │     │         │          │     │         │          │
│  │  }            │ │     │         │          │     │         │          │
│  └───────────────┘ │     │         │          │     │         │          │
│                    │     │         │          │     │         │          │
└────────────────────┘     └────────────────────┘     └────────────────────┘
       Layer 3                    Layer 2                    Layer 1
   (Control Layer)            (Process Layer)              (Data Layer)

ERROR FLOW DIRECTION (from API to user) ──────────────────────────────────▶

┌───────────────────────────────────────────────────────────────────────────┐
│                     ERROR PROPAGATION MECHANISMS                          │
├───────────────────┬───────────────────────────┬───────────────────────────┤
│ BrainService      │ DialogueChain             │ ConversationAgent         │
│ (Layer 1)         │ (Layer 2)                 │ (Layer 3)                 │
├───────────────────┼───────────────────────────┼───────────────────────────┤
│ • Uses Result<T>  │ • Uses getOrThrow()       │ • Uses try-catch blocks   │
│ • Wraps API errors│ • Propagates exceptions   │ • Calls handleError()     │
│ • Returns failure │ • No try-catch for API    │ • Creates user responses  │
│                   │   errors                  │                           │
└───────────────────┴───────────────────────────┴───────────────────────────┘
```

## Core Philosophy: Different Layers, Different Error Philosophies

1.  **`BrainService` (Layer 1 - Data Layer / External Communication):**
    *   **Role:** Interacts directly with external services (e.g., Google Gemini API).
    *   **Error Handling Mechanism:** Primarily uses `kotlin.Result<T>`.
    *   **Rationale:** Network issues or API errors are considered *expected variations* or common failure modes when dealing with external dependencies. `Result.success(data)` or `Result.failure(exception)` explicitly forces the caller (`DialogueChain`) to acknowledge and handle both successful outcomes and predictable failures without immediately crashing the flow.

2.  **`DialogueChain` (Layer 2 - Process Layer / Conversation Orchestration):**
    *   **Role:** Manages the logic of the conversation, calls `BrainService` for LLM tasks.
    *   **Error Handling Mechanism:** Propagates critical errors by **throwing exceptions**.
    *   **Rationale:** If `BrainService` returns a `Result.failure(NetworkException)` (or similar critical error), `DialogueChain` usually cannot fulfill its own function (e.g., extract themes, generate a response). For `DialogueChain`, this is an *exceptional condition* that halts its internal logic. Instead of returning a `Result` itself for these propagated errors, it converts the `BrainService` failure into a thrown exception (typically by using `getOrThrow()` on the `Result` from `BrainService`). This signals a more severe problem upwards and keeps `DialogueChain`'s internal code focused on its primary tasks rather than extensive `Result` checking.

3.  **`ConversationAgent` (Layer 3 - Control Layer / User Experience Management):**
    *   **Role:** Orchestrates the highest-level interaction flow, manages UI state, and handles errors in a user-facing way.
    *   **Error Handling Mechanism (for `DialogueChain` calls):** Catches exceptions thrown by `DialogueChain` using `try-catch` blocks and then calls the centralized `handleError(exception, customOnFailure)` method.
    *   **Rationale:** `ConversationAgent` is the ultimate point where system-level errors need to be translated into a coherent user experience (e.g., showing an error message, resetting the conversation state). The `handleError` method provides a consistent way to manage different types of errors, especially `NetworkException`.

## The Two Error Handling Mechanisms

The system employs two distinct but complementary error handling mechanisms:

1. **Result-based Error Handling (Layer 1)**
   * **Where**: Used primarily in `BrainService` (the data layer)
   * **How**: Operations that may fail return a `kotlin.Result<T>` object
   * **Purpose**: Encapsulates success (with data) or failure (with exception) in a type-safe manner
   * **Example Functions**:
     * `BrainService.getCheckInEvaluation()`
     * `BrainService.extractThemesFromCurrentUserMessage()`
     * `BrainService.getCheckInResponseWithThemes()`

2. **Exception-based Error Handling (Layers 2-3)**
   * **Where**: Used in `DialogueChain` (propagation) and `ConversationAgent` (handling)
   * **How**: Exceptions are thrown by `DialogueChain` (originating from `BrainService` `Result.failure` via `getOrThrow()`) and caught by `ConversationAgent`.
   * **Purpose**: Provides immediate error notification and forces handling at the appropriate layer.
   * **Example Functions**:
     * `DialogueChain.evaluateTransition()` - converts `Result.failure` to a thrown exception.
     * `ConversationAgent.progressCheckIn()` - catches exceptions from `DialogueChain.processTurn()` and calls `handleError()`.

## Error Flow: From API to User Interface

The following describes the precise flow of an error through the system:

### 1. Error Origin: BrainService (Layer 1)
* When an operation like calling the Gemini API fails in `BrainService`:
  * The error (e.g., network timeout, invalid response) is caught internally.
  * It is wrapped in a `Result.failure(exception)` object.
  * The original exception (e.g., `IOException`, `JsonParseException`) is preserved inside.
  * This `Result` object is returned from methods like `getCheckInEvaluation()`.

### 2. Error Propagation: DialogueChain (Layer 2)
* When `DialogueChain` methods like `evaluateTransition()` receive a `Result` from `BrainService`:
  * They call `result.getOrThrow()` to extract the success value.
  * If the `Result` contains a failure, `getOrThrow()` **throws the original exception**.
  * The `DialogueChain` does NOT catch these exceptions from `BrainService` calls.
  * The exception propagates up through the call chain (e.g., from `evaluateTransition()` → `processTurn()`).
  * The exception ultimately escapes `DialogueChain.processTurn()`.

### 3. Error Handling: ConversationAgent (Layer 3)
* When `ConversationAgent.progressCheckIn()` calls `checkInDialogueChain.processTurn()`:
  * This call is wrapped in a `try-catch` block.
  * If an exception propagates from `DialogueChain`, the `catch` block in `progressCheckIn` catches it.
  * The `catch` block then calls `handleError(e, null)`.
  * `handleError` identifies the error type (e.g., `NetworkException` vs. other exceptions).
  * It logs the error, creates an appropriate user-facing response (e.g., by calling `speak()`), and/or resets the conversation state (e.g., by calling `toggleBrainConversation(isNetworkError = true)` for network errors).

## Why This Dual-Mechanism Approach?

The system architecture uses these two complementary mechanisms because:

1. **Result provides type-safety at boundaries**:
   * At the external API boundary (`BrainService`), `Result` objects clearly indicate that operations may fail.
   * This enforces explicit handling of success/failure paths.
   * Network operations naturally fit the Result pattern as they frequently fail in expected ways.

2. **Exceptions provide clear flow control**:
   * For the processing and control layers (`DialogueChain`, `ConversationAgent`), exceptions provide a natural way to immediately jump to error handling.
   * The propagation path is clear: errors flow up until caught by the appropriate handler in `ConversationAgent`.
   * The `DialogueChain` layer stays focused on the "happy path" logic without being cluttered with its own error handling for `BrainService` failures.

## Simplified Error Handling in `ConversationAgent.progressCheckIn`

`ConversationAgent.progressCheckIn()` now directly calls `checkInDialogueChain.processTurn()` within a `try-catch` block.
If `processTurn()` throws an exception, the `catch` block calls the `handleError(e, null)` method.
`handleError` remains the centralized function for processing different types of errors, speaking to the user, and resetting states (like calling `toggleBrainConversation(isNetworkError = true)` for `NetworkException`).

## Sequence of Events (Concrete Example with Simplified Handling)

For a network failure during transition evaluation:

1. User message is received by `ConversationAgent.progressCheckIn()`.
2. The call to `checkInDialogueChain.processTurn(userWishes)` is made within a `try` block.
3. `DialogueChain.processTurn()` calls `evaluateTransition()`.
4. `evaluateTransition()` calls `brainService.getCheckInEvaluation(prompt)`.
5. Network error occurs in `BrainService` when calling Gemini API.
6. `BrainService` catches the `IOException` and returns `Result.failure(IOException)`.
7. `DialogueChain.evaluateTransition()` calls `result.getOrThrow()`.
8. The `IOException` is thrown and propagates up through `processTurn()`.
9. The `catch (e: Exception)` block in `ConversationAgent.progressCheckIn()` catches this `IOException`.
10. `handleError(IOException, null)` is called.
11. `handleError` identifies the `IOException` as a `NetworkException` (or a derivative) and calls `toggleBrainConversation(isNetworkError = true)`.
12. The UI shows the user a friendly message about network unavailability, and the conversation state is reset.

This simplified architecture ensures errors are handled at the appropriate level:
* `BrainService` detects and encapsulates external errors as `Result.failure`.
* `DialogueChain` converts these critical failures into thrown exceptions to signal processing halts.
* `ConversationAgent` catches these exceptions and uses `handleError` for consistent, user-facing error management. 