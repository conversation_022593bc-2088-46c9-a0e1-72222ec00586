# VoxManifestor Context Index

*A navigation guide to VoxManifestor's documentation hierarchy for developers, AI agents, and stakeholders.*

## Documentation Hierarchy

VoxManifestor's context follows a **conceptual hierarchy** from theoretical foundation to implementation details.

```
manifestation_context.md (Theoretical DNA)
    ↓
project_context.md (Product Vision & Requirements) 
    ↓
core_loop_context.md (Core Loop Conceptual Architecture)
    ↓
codebase_context.md (Technical Architecture Overview)
    ↓
convo_agent_context.md (Central Agent Controller Deep-Dive)
    ├── conversation_states_and_ui.md (State & UI Details)
    └── checkin_context.md (Check-In Sub-system Deep-Dive)
        └── checkin_error_handling.md (Error Handling Details)
```

---

## When to Use Each Context

### 🧠 [manifestation_context.md](manifestation_context.md) - **Theoretical Foundation**

**Use this for:**
- Understanding the philosophical basis of VoxManifestor
- Training AI agents on conversation strategy
- Explaining the "why" behind design decisions

**Key Content:** 14-step manifestation process, signal coherence theory, brain science foundation.

**Audience:** Product managers, conversation designers, AI trainers, stakeholders.

---

### 🎯 [project_context.md](project_context.md) - **Product Vision & Strategy**

**Use this for:**
- Project onboarding and team alignment
- Understanding user workflows, use cases, and MVP definition
- Business context and high-level development planning

**Key Content:** User workflows, Core Loop design, MVP definition, conversational challenges.

**Audience:** Product teams, new developers, project managers, business stakeholders.

---

### ➰ [core_loop_context.md](core_loop_context.md) - **Core Loop Conceptual Architecture**

**Use this for:**
- Understanding the primary conversational cycle of the app
- Grasping the phased approach to user guidance (Check-in, Wish Collection, etc.)
- Designing new features that integrate into the main user journey

**Key Content:** The recurring coaching cycle, phase descriptions (Check-in, Exploration), metric-based progression.

**Audience:** Developers, conversation designers, product managers.

---

### ⚡ [codebase_context.md](codebase_context.md) - **Technical Architecture Overview**

**Use this for:**
- General development work and code contributions
- Understanding the overall system architecture, data flow, and module organization
- Code reviews and high-level architectural decisions

**Key Content:** Module breakdown, architecture patterns (MVVM, Repository), file structure, key dependencies.

**Audience:** Developers, technical leads, AI coding assistants.

---

### 🤖 [convo_agent_context.md](convo_agent_context.md) - **Central Agent Controller Deep-Dive**

**Use this for:**
- Working on the central `ConversationAgent` orchestrator
- Understanding how different conversational paradigms (Idle vs. Active) are managed
- Modifying the main conversation lifecycle or high-level state orchestration

**Key Content:** `ConversationAgent` responsibilities, state management, interaction with sub-systems (`BrainService`, `DialogueChain`), error handling.

**Audience:** Developers working on the core agent logic.

---

### states_and_ui.md) - **State & UI Details**

**Use this for:**
- Developing or debugging UI components that interact with the agent (e.g., `AgentToolbar`)
- Understanding the link between `DialogueState`, `RecognitionState`, and UI behavior
- Implementing new state-driven features in the UI

**Key Content:** Detailed breakdown of `DialogueState`, `RecognitionState`, `VoxInputType`; UI button state logic; state transition diagrams.

**Audience:** UI/UX developers, developers working on agent-UI integration.

---

### 🔗 [checkin_context.md](checkin_context.md) - **Check-In Sub-system Deep-Dive**

**Use this for:**
- Working specifically on the initial "Check-In" phase of the Core Loop
- Understanding the `DialogueChain` (transition evaluation, theme extraction, strategy selection, response generation)
- Debugging issues related to conversation themes, strategies, or transitions

**Key Content:** Detailed DialogueChain system, dual-chain architecture, data flow, integration points with `ConversationAgent`.

**Audience:** Developers working on the conversation system, AI integration specialists.

---

### 🚨 [checkin_error_handling.md](checkin_error_handling.md) - **Error Handling Details**

**Use this for:**
- Understanding how errors propagate from the `BrainService` to the UI during the check-in process
- Debugging API or network-related failures within the `DialogueChain`
- Modifying the error handling strategy for the check-in system

**Key Content:** Error propagation architecture (Result vs. Exceptions), flow diagrams, sequence of events for failures.

**Audience:** Developers debugging or improving system resilience.

---

## Quick Navigation

### For New Team Members
1. Start with [manifestation_context.md](manifestation_context.md) for philosophical grounding.
2. Read [project_context.md](project_context.md) for product understanding.
3. Review [core_loop_context.md](core_loop_context.md) to understand the main user journey.
4. Reference [codebase_context.md](codebase_context.md) for a technical overview.

### For Development Work
- **General Architecture**: [codebase_context.md](codebase_context.md)
- **Core Agent Logic**: [convo_agent_context.md](convo_agent_context.md)
- **Check-In Features**: [checkin_context.md](checkin_context.md)
- **UI & State**: [conversation_states_and_ui.md](conversation_states_and_ui.md)
- **Error Handling**: [checkin_error_handling.md](checkin_error_handling.md)

### For AI Agents
- **Task Understanding**: [project_context.md](project_context.md) + [codebase_context.md](codebase_context.md)
- **Conversation Work**: [manifestation_context.md](manifestation_context.md) + [checkin_context.md](checkin_context.md)
- **Architecture Changes**: [codebase_context.md](codebase_context.md) + [convo_agent_context.md](convo_agent_context.md)

---

## Context Maintenance

### Update Triggers
- **manifestation_context.md**: Core philosophy refinements.
- **project_context.md**: MVP changes, new user workflows.
- **core_loop_context.md**: Changes to the main conversational phases.
- **codebase_context.md**: Major refactors, new modules or dependencies.
- **convo_agent_context.md**: Changes to the `ConversationAgent` or its core responsibilities.
- **checkin_context.md**: Changes to the `DialogueChain` or check-in flow.
- **checkin_error_handling.md**: Updates to the error propagation model.
- **conversation_states_and_ui.md**: New UI states or agent-UI interactions.

### Cross-References
All documents should maintain **bidirectional links** to related concepts across the hierarchy. When updating any document, verify cross-references remain accurate.

---

*This index serves as the entry point for all VoxManifestor documentation. Keep it updated as the project evolves.*