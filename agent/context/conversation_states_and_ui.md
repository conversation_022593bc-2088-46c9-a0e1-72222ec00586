# VoxManifestorApp Conversation State Management Documentation

## Overview

This document focuses specifically on the state management and UI interaction patterns that enable VoxManifestor's voice-driven conversational interface. It serves as a reference for understanding:

1. **Core State Types**: The fundamental states that control the agent's behavior and UI presentation
2. **State Transitions**: How the system moves between different states during user interaction
3. **UI Integration**: How these states are exposed to and controlled by the UI layer
4. **Operational Paradigms**: The different modes of operation and how they affect state management

This document is particularly important for:
- Understanding the relationship between voice recognition states and UI presentation
- Implementing new UI components that need to respond to agent state
- Debugging state-related issues in the conversational flow
- Adding new state-driven features to the agent system

For related topics, see:
- `checkin_context.md` for Check-In phase specific state management
- `core_loop_context.md` for Core Loop state progression
- `convo_agent_context.md` for the ConversationAgent's role in state management

## 1. Fundamental Agent & UI States

These are the core states that underpin all agent interactions and UI representations.

### 1.1. RecognitionState (Enum)

Controls the microphone and speech recognition status. This is the most basic state for voice interaction.

```kotlin
enum class RecognitionState {
    Inactive,           // Microphone off
    Listen_Active,      // Microphone on and listening
    Listen_Pause,       // Microphone temporarily paused
    Permission_Denied   // Cannot access microphone
}
```

### 1.2. DialogueState (Sealed Class)

Represents the immediate interaction state between the agent and user at any given moment.

```kotlin
sealed class DialogueState {
    data object Speaking : DialogueState()         // Agent is actively speaking
    data class ExpectingInput(                     // Agent is waiting for user input
        val inputType: VoxInputType
    ) : DialogueState()  
    data object Idle : DialogueState()             // No active dialogue interaction
    data object Thinking : DialogueState()         // Agent is processing (e.g., waiting for LLM)
}
```

### 1.3. VoxInputType (Enum)

Specifies the type of input expected from the user when `DialogueState` is `ExpectingInput`. This helps the agent process the user's speech correctly.

```kotlin
enum class VoxInputType {
    FREEFORM,           // Used for long-form text or unclassified inputs
    YES_NO,             // Used when expecting a yes/no response
    COMMAND_SELECTION,  // Used when user is selecting a process/option from a menu
    BRAIN_RESPONSE      // Used when the input is part of an LLM-driven conversation (Core Loop, Concept Building)
}
```
- **FREEFORM**: General user input without specific structure requirements. Often used in initial phases of command-driven flows.
- **YES_NO**: Simple confirmation responses, common in validation steps of command-driven flows.
- **COMMAND_SELECTION**: Used when the agent presents a menu of actions (e.g., "define, change, or delete this wish?").
- **BRAIN_RESPONSE**: Indicates the input is part of a structured, LLM-driven conversation and will be processed accordingly.

### 1.4. AgentCortex: Central State Management

The `AgentCortex` serves as the central nervous system of VoxManifestor's conversational architecture. It implements a unidirectional data flow pattern where state is updated exclusively by the `ConversationAgent` and observed by the UI through reactive `StateFlow`s.

#### 1.4.1. Architectural Role

- **Single Source of Truth**: Acts as the canonical data store for all agent-related state
- **State Isolation**: Ensures state mutations only occur through controlled channels
- **Reactive Interface**: Exposes state as `StateFlow`s for UI observation
- **Intent Channel**: Provides a buffered communication channel for UI→Agent communication

#### 1.4.2. State Categories

The state managed by `AgentCortex` falls into several key categories:

1. **Dialogue & Interaction States**
   - `DialogueState`: Current interaction mode (Speaking, ExpectingInput, etc.)
   - `DisplayState`: UI presentation state
   - `ConversationType`: Current conversation paradigm (CoreLoop, ConceptBuilding, etc.)

2. **Conversation History & Content**
   - `ConversationHistory`: Complete dialogue record
   - `RawSpeechText`: Current speech recognition output
   - `CommandState`: Active voice command

3. **Core Loop & Check-In States**
   - `CoreLoopState`: Progress through the 7-phase Core Loop
   - `CheckInState`: Check-In phase specific state (themes, metrics, etc.)

#### 1.4.3. Communication Patterns

1. **UI → Agent Communication**
   - Uses `UiIntent` sealed class to define possible UI actions
   - Implements a buffered `SharedFlow` for intent delivery
   - Provides non-suspending `submitIntent()` for ViewModel use

2. **Agent → State Updates**
   - All state mutations occur through internal methods
   - Updates are performed exclusively by `ConversationAgent`
   - Methods are marked `internal` to enforce access control

3. **State → UI Observation**
   - UI components observe state through `StateFlow`s
   - `AgentViewModel` provides a clean interface for UI state access
   - State changes automatically propagate to observing components

## 2. Agent Operational Paradigms

The agent operates under two primary paradigms, dictating how it interprets user input and behaves.

### 2.1. Idle / Command-Listening Paradigm

**Purpose**: Allows direct, administrative control over app functions and content via specific voice commands. This is the agent's default state when no extended conversation is active.

**Characteristics**:
- `DialogueState` is typically `Idle`.
- `ConversationType` (see below) is `null`.
- The agent actively listens for keywords defined in `VoiceCommandEntity.kt`.
- Interactions are generally transactional (e.g., "read my wishes," "select wish three").

**Implementation**:
- Voice input is matched against keywords in `VoiceCommandEntity`.
- Matched commands trigger specific handler functions in `ConversationAgent.kt` (e.g., `handleReadCommand()`, `handleSelectCommand()`).
- Some commands may initiate a "Structured Command-Driven Flow" (see 2.2.1).

### 2.2. Active Conversation Paradigm

**Purpose**: Engages the user in more extended, stateful dialogues, either through structured flows or dynamic LLM-driven interactions.

**Characteristics**:
- `DialogueState` is typically `ExpectingInput` or `Speaking` or `Thinking`.
- `ConversationType` is non-null, indicating the nature of the active conversation.

This paradigm has two main sub-categories:

#### 2.2.1. Structured Command-Driven Flows

**Purpose**: Guide the user through specific, multi-step tasks that are initiated by a voice command but require a sequence of inputs.

**Characteristics**:
- Initiated by commands from the "Idle / Command-Listening Paradigm" (e.g., `START` command leading to wish collection, `SELECT` command leading to wish action selection).
- Follow a predefined sequence of steps, managed by a simple state machine.
- Less dynamic than LLM-driven conversations but more interactive than single commands.

**Relevant States**:
- **`ConversationType`**: `WishCollection`, `WishSelection`. These indicate that a structured, command-initiated flow is active.
  ```kotlin
  // From AgentClasses.kt
  sealed class ConversationType {
      // ... other types ...
      object WishCollection : ConversationType()     // Collecting a new wish
      object WishSelection : ConversationType()      // Working with existing wishes
      // ... other types ...
  }
  ```
- **`ConversationStep` (Sealed Class)**: Tracks the current stage within these structured flows. This state is crucial for `ConversationAgent` to know what to do next in the sequence.
  ```kotlin
  // From AgentClasses.kt
  sealed class ConversationStep {
      // For wish collection conversation (initiated by START command)
      sealed class WishStep : ConversationStep() {
          object AskForWish : WishStep()         // Agent: "What is your wish?"
          object CaptureWish : WishStep()        // Agent listens for wish description
          object CheckWish : WishStep()          // Agent: "I heard [wish]. Is that correct?"
          object CaptureValidation : WishStep()  // Agent listens for yes/no
      }

      // For wish selection & action conversation (initiated by SELECT command)
      sealed class WishSelectionStep : ConversationStep() {
          object SelectWish : WishSelectionStep()      // Agent: "Which wish number?"
          object CaptureWish : WishSelectionStep()     // Agent listens for wish number
          object SelectProcess : WishSelectionStep()   // Agent: "Define, change, or delete?"
          object CaptureProcess : WishSelectionStep()  // Agent listens for action choice
      }
  }
  ```

#### 2.2.2. LLM-Driven Advanced Conversations

**Purpose**: Provide sophisticated, AI-powered coaching and conceptualization experiences.

**Characteristics**:
- Highly dynamic, relying on `BrainService` (LLM) for natural language understanding and response generation.
- Involves complex conversational context, theme extraction, and strategy selection.
- Aims for a more natural, human-like dialogue.

**Relevant States**:
- **`ConversationType`**: `CoreLoop` (the main multi-phase coaching journey, including `CheckIn`), `ConceptBuilding` (structured dialogues on the Concept screen), `ConceptMenu` (initial interaction on the Concept screen).
  ```kotlin
  // From AgentClasses.kt
  sealed class ConversationType {
      object CoreLoop : ConversationType()           // Structured coaching conversation
      object ConceptBuilding : ConversationType()    // Concept screen interaction
      object ConceptMenu : ConversationType()        // Initial menu in concept screen
      // ... other types ...
  }
  ```
- These conversation types often involve their own internal state management (e.g., `CoreLoopState`, `CheckInState`).

### 2.3. Transitions Between Paradigms

- **Idle/Command-Listening → Active Conversation**:
  - Typically occurs when a `VoiceCommandEntity` either directly starts an LLM-driven flow (e.g., a future command to "start check-in") or initiates a "Structured Command-Driven Flow" (e.g., `START` -> `WishCollection`).
  - `ConversationAgent.toggleBrainConversation()` can also explicitly start LLM-driven advanced conversations.
  - `ConversationType` becomes non-null.
  - `DialogueState` often transitions from `Idle` to `Speaking` (as agent responds/prompts) and then to `ExpectingInput`.

- **Active Conversation → Idle/Command-Listening**:
  - User issues a `STOP` command.
  - A structured or LLM-driven conversation flow completes or is explicitly terminated (e.g., `ConversationAgent.toggleBrainConversation()`).
  - `ConversationType` is set to `null`.
  - `DialogueState` transitions to `Idle`.

**Note**: When in any "Active Conversation" state (especially LLM-Driven), most `VoiceCommandEntity` keywords are ignored (except for universal commands like `STOP`). Input is primarily treated as content for the ongoing dialogue rather than a new command.

## 3. UI Control: AgentToolbar Button States

The `AgentToolbar` uses the above states to dynamically control its four main buttons:

### 3.1. Microphone Button
- **Enabled**: Always (unless `RecognitionState` is `Permission_Denied`).
- **Active When**: `RecognitionState == Listen_Active`.
- **Purpose**: Toggle voice recognition on/off.

### 3.2. Interrupt Button
- **Enabled**: Only when `DialogueState == Speaking`.
- **Visual**: Appears red when agent is speaking, grayed out otherwise.
- **Purpose**: Stop the agent from speaking immediately.

### 3.3. Toggle Conversation Button
- **Enabled**: Always.
- **Visual**:
  - Shows red "stop" icon when `DialogueState != Idle` (indicating an active conversation or process that can be stopped).
  - Shows green "go" icon when `DialogueState == Idle` (indicating readiness to start a new conversation).
- **Purpose**: Start or end an LLM-driven "Advanced Conversation" (like CoreLoop). Also stops "Structured Command-Driven Flows".

### 3.4. Send Response Button
- **Enabled**: Only when `DialogueState` is `ExpectingInput`.
- **Visual**:
  - Green with light background when enabled.
  - Gray when disabled.
- **Purpose**: Manually submit the currently captured voice input to the agent for processing. Primarily used when `VoxInputType` is `BRAIN_RESPONSE` or during other specific input expectations.

## 4. State Transition Diagrams

### 4.1. DialogueState Transitions
```
┌──────┐     speak()      ┌─────────┐  
│ Idle ├──────────────────► Speaking │  
└──┬───┘                  └────┬────┘  
   │                           │       
   │ startConversation()       │ finish speaking / expect input
   │ (sets inputType)          │       
   ▼                           ▼       
┌──────────────┐  process()  ┌─────────┐  
│ExpectingInput◄─────────────┤ Thinking │  
└──────────────┘  LLM/Logic └─────────┘  
                  response
```

### 4.2. High-Level ConversationType Transitions (Illustrative)
```
            ┌─────────────────┐
            │                 │
Null (Idle) │                 │ (LLM-Driven)
   ▲        │                 │
   │        ├─────►CoreLoop◄──┼────►ConceptBuilding
   │        │                 │
stop cmd    │                 │
   │        │                 │ (Command-Driven Flows)
   └────────┤ WishCollection / WishSelection 
            │                 │
            └─────────────────┘
        (Active Conversation Paradigm)
```
*Note: This diagram simplifies the complex interactions and transitions between various ConversationTypes. Specific commands or UI actions trigger these changes.*

## 5. Implementation Best Practices

1.  **State Updates**:
    *   Always use `AgentCortex` update methods to modify agent-related states (`DialogueState`, `ConversationType`, etc.).
    *   Avoid direct mutation of state variables from UI or other components.

2.  **Component Design**:
    *   UI components should observe state (from ViewModels) but not modify it directly.
    *   Use callbacks (e.g., `onMicClick`, `onSendResponse` in `AgentToolbar`) to request state changes, which are then handled by `ConversationAgent` via `AgentCortex`.

3.  **Synchronization**:
    *   Ensure `DialogueState` and `ConversationType` are managed coherently. For example, `ConversationType` should generally be `null` if `DialogueState` is `Idle` (in Command-Listening Paradigm).
    *   When starting any active conversation, set both `ConversationType` and `DialogueState` appropriately.

4.  **Error Handling**:
    *   Include fallbacks for unexpected state combinations or errors during state transitions.
    *   Log state transitions for debugging purposes.

5.  **Testing**:
    *   Test different combinations of `DialogueState`, `VoxInputType`, and `ConversationType`.
    *   Verify UI buttons (`AgentToolbar`) behave correctly for each state combination and paradigm.
    *   Test transitions between Idle/Command-Listening and various Active Conversation states.

## 6. Testing Procedure for Button States

To verify proper state management and UI responsiveness, test these scenarios:

1.  **Initial State (Idle / Command-Listening Paradigm)**:
    *   App starts. `DialogueState` should be `Idle`. `ConversationType` should be `null`.
    *   Microphone Button: Can be toggled.
    *   Interrupt Button: Disabled.
    *   Toggle Conversation Button: Shows "go" icon.
    *   Send Response Button: Disabled and gray.

2.  **Starting LLM-Driven Conversation (e.g., CoreLoop)**:
    *   Click Toggle Conversation button (now showing "go").
    *   `ConversationType` should become `CoreLoop`.
    *   `DialogueState` should transition (e.g., to `Speaking` then `ExpectingInput(BRAIN_RESPONSE)`).
    *   Toggle Conversation Button: Shows "stop" icon.
    *   Send Response Button: Becomes enabled when `DialogueState` is `ExpectingInput`.

3.  **During Agent Speaking (Any Active Conversation)**:
    *   When `DialogueState == Speaking`:
        *   Interrupt Button: Enabled and red.
        *   Send Response Button: Disabled.

4.  **During Agent Thinking (Any Active Conversation)**:
    *   When `DialogueState == Thinking`:
        *   Interrupt Button: Disabled.
        *   Send Response Button: Disabled.

5.  **During Agent Expecting Input (Any Active Conversation)**:
    *   When `DialogueState == ExpectingInput(...)`:
        *   Interrupt Button: Disabled.
        *   Send Response Button: Enabled and green.

6.  **Ending LLM-Driven Conversation**:
    *   Click Toggle Conversation button (now showing "stop").
    *   `DialogueState` should return to `Idle`.
    *   `ConversationType` should return to `null`.
    *   Toggle Conversation Button: Shows "go" icon.
    *   Send Response Button: Disabled and gray.

7.  **Structured Command-Driven Flow (e.g., WishCollection after "start" command)**:
    *   From Idle, issue "start" command.
    *   `ConversationType` should become `WishCollection`.
    *   `DialogueState` should cycle through `Speaking`, `ExpectingInput` (with appropriate `VoxInputType` for each `ConversationStep`).
    *   Toggle Conversation Button: Shows "stop" icon (as an active conversation is in progress).
    *   User can use `STOP` command or Toggle Conversation button to exit this flow and return to Idle/Command-Listening.

Testing these scenarios thoroughly will help ensure the state management is robust and the UI is consistently reflective of the agent's operational paradigm and immediate dialogue state.
