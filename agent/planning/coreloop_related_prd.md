# Core Loop Enhancement PRD

## Background
The Core Loop represents VoxManifestor's core AI-driven manifestation workflow, guiding users through a structured 7-phase conversation process. Following comprehensive analysis and successful CoreLoopManager extraction, this PRD documents the current state, completed improvements, and future enhancement opportunities.

## Current State Analysis

### ✅ What Works Well
- **Complete 7-phase conversation flow** (CHECK_IN → WISH_COLLECTION → PRESENT_STATE_EXPLORATION → DESIRED_STATE_EXPLORATION → CONTRAST_ANALYSIS → AFFIRMATION_PROCESS → LOOP_DECISION)
- **Check-In DialogueChain integration** (sophisticated 4-chain conversation system with theme extraction)
- **Phase completion tracking** (per-wish phase status in CoreLoopState)
- **Wish selection algorithm** (prioritizes incomplete phases using WishPriorityManager)
- **LLM integration** (BrainService handles all AI interactions with tool execution)
- **Modular architecture** (CoreLoopManager extracted from ConversationAgent)
- **Clean separation of concerns** (ConversationAgent orchestrates, CoreLoopManager processes)

### ✅ Recently Completed Improvements

#### 1. **CoreLoopManager Extraction** (COMPLETED ✅)
**Achievement**: Successfully extracted Core Loop functionality into dedicated CoreLoopManager
- Reduced ConversationAgent complexity by ~600 lines
- Implemented "frontal cortex" pattern (ConversationAgent orchestrates, CoreLoopManager processes)
- Maintained all existing functionality during extraction
- Improved code organization and separation of concerns

#### 2. **Voice Command Routing Fix** (COMPLETED ✅)
**Achievement**: Fixed "start" voice command to route to Core Loop instead of legacy CommandMode
- Before: "start" → `commandMode.enterConversationLoop()` (legacy wish collection)
- After: "start" → `handleCoreLoopIntent()` (modern Core Loop)
- Consistent entry points across UI toggle and voice commands

#### 3. **Code Cleanup and Deduplication** (COMPLETED ✅)
**Achievement**: Removed duplicate and unused functions from ConversationAgent
- Removed 7 unused/duplicate functions (getOrdinal, findNextEmptyWishSlot, etc.)
- Eliminated unnecessary wrapper functions
- Simplified function call chains
- Improved code maintainability

### ❌ Remaining Critical Weaknesses

#### 1. **State Persistence Gap** (High Priority)
**Problem**: Every app launch is a fresh start - no conversation state persists
- CoreLoopState resets to defaults (CHECK_IN, currentWishIndex = -1)
- CheckInState resets (no themes, engagement metrics, or stage)
- User loses conversation context and progress

**Impact**:
- Repetitive user experience (same Check-In flow every time)
- Lost conversation themes and engagement patterns
- No sense of progress or continuity

#### 2. **Resume Logic Not Implemented** (Medium Priority)
**Problem**: `resumeCoreLoop()` function exists but is never called
- No logic to determine when to resume vs. start fresh
- No "where we left off" restoration
- Conversation history persists but state doesn't

#### 3. **Concept Screen Integration** (Medium Priority)
**Problem**: Core Loop phases 2-7 need integration with concept screen for present/desired state editing
- Current Core Loop processing is simplified
- Concept building functions still in ConversationAgent
- Need transitions between Core Loop and concept screen

## Enhancement Roadmap

### Phase 1: Core Loop Extraction (COMPLETED ✅)
**Goal**: Extract Core Loop functionality into dedicated CoreLoopManager
**Timeline**: Completed December 2024
**Dependencies**: None

**Tasks**:
- [x] Complete flow analysis and function mapping
- [x] Extract core functions to CoreLoopManager
- [x] Remove duplicate and unused functions
- [x] Fix voice command routing
- [x] Test integration
- [x] Clean up ConversationAgent (removed 7 unused functions)

**Achievements**:
- Successfully created CoreLoopManager with simplified interface
- Maintained existing functionality while improving code organization
- Fixed voice command routing to use Core Loop instead of legacy CommandMode
- Reduced ConversationAgent complexity significantly
- Established foundation for future Core Loop enhancements

### Phase 2: Concept Screen Integration (High Priority)
**Goal**: Integrate Core Loop phases 2-7 with concept screen for present/desired state editing
**Timeline**: Next sprint
**Dependencies**: Phase 1 completion

**Tasks**:
- [ ] Extract concept building functions from ConversationAgent
- [ ] Design Core Loop ↔ Concept Screen transition flow
- [ ] Implement concept screen integration in CoreLoopManager
- [ ] Add concept repository interactions to Core Loop phases
- [ ] Test integrated present/desired state exploration

**Technical Approach**:
```kotlin
// In CoreLoopManager
suspend fun processPhaseWithConceptIntegration(phase: ConversationPhase) {
    when (phase) {
        PRESENT_STATE_EXPLORATION -> initiateConceptBuilding(ConceptType.Present)
        DESIRED_STATE_EXPLORATION -> initiateConceptBuilding(ConceptType.Desired)
        // ... other phases
    }
}
```

### Phase 3: State Persistence Implementation (High Priority)
**Goal**: Implement conversation state persistence across app launches
**Timeline**: Future sprint
**Dependencies**: Phase 2 completion

**Tasks**:
- [ ] Add CoreLoopState persistence to DataStore
- [ ] Add CheckInState persistence to DataStore
- [ ] Implement state restoration on app launch
- [ ] Add "resume vs. fresh start" decision logic
- [ ] Test cross-session continuity

**Technical Approach**:
```kotlin
// Add to UserPreferencesRepository
private val CORE_LOOP_STATE = stringPreferencesKey("core_loop_state")
private val CHECK_IN_STATE = stringPreferencesKey("check_in_state")

suspend fun saveCoreLoopState(state: CoreLoopState)
suspend fun restoreCoreLoopState(): CoreLoopState?
```

### Phase 4: Enhanced Resume Experience (Medium Priority)
**Goal**: Implement intelligent conversation resumption
**Timeline**: Future sprint
**Dependencies**: Phase 3 completion

**Tasks**:
- [ ] Implement conversation context analysis
- [ ] Add "Welcome back" flow with context summary
- [ ] Implement user choice (resume vs. fresh start)
- [ ] Add conversation gap detection (time since last session)

**User Experience Flow**:
```
App Launch → Check for previous state
  ↓
If recent conversation exists:
  "Welcome back! Last time we were exploring [context].
   Would you like to continue where we left off?"
  ↓
User choice → Resume with context OR Start fresh
```

### Phase 5: Progress Tracking & Analytics (Low Priority)
**Goal**: Add comprehensive progress tracking and user insights
**Timeline**: Future sprint
**Dependencies**: Phase 3 completion

**Tasks**:
- [ ] Implement phase completion analytics
- [ ] Add conversation quality metrics
- [ ] Create progress visualization
- [ ] Add engagement pattern analysis

## Success Criteria

### Phase 1 (Core Loop Extraction) - COMPLETED ✅
- ✅ CoreLoopManager successfully extracted
- ✅ All existing functionality preserved
- ✅ Voice command routing fixed
- ✅ Code duplication eliminated
- ✅ ConversationAgent cleanup completed (7 unused functions removed)
- ✅ "Frontal cortex" pattern implemented

### Phase 2 (Concept Screen Integration)
- ✅ Concept building functions extracted from ConversationAgent
- ✅ Core Loop phases integrate with concept screen
- ✅ Present/desired state exploration works seamlessly
- ✅ Concept repository interactions implemented
- ✅ Smooth transitions between Core Loop and concept screen

### Phase 3 (State Persistence)
- ✅ Conversation state persists across app launches
- ✅ User can resume where they left off
- ✅ Themes and engagement metrics carry forward
- ✅ No data loss on app termination

### Phase 4 (Enhanced Resume)
- ✅ Intelligent resume vs. fresh start decisions
- ✅ Context-aware welcome back messages
- ✅ User choice in conversation continuation
- ✅ Seamless conversation flow restoration

### Phase 5 (Progress Tracking)
- ✅ Comprehensive progress analytics
- ✅ User engagement insights
- ✅ Conversation quality metrics
- ✅ Visual progress indicators

## Technical Considerations

### State Persistence Strategy
- **Use DataStore** for structured state persistence (JSON serialization)
- **Implement versioning** for state schema evolution
- **Add migration logic** for state format changes
- **Consider encryption** for sensitive conversation data

### Performance Considerations
- **Lazy state restoration** (only when needed)
- **Background state saving** (don't block UI)
- **State compression** for large conversation histories
- **Cleanup old state data** (retention policies)

### Error Handling
- **Graceful degradation** if state restoration fails
- **Fallback to fresh start** on corruption
- **State validation** before restoration
- **Recovery mechanisms** for partial state loss

## Future Considerations from Analysis

### Architecture Insights
Based on the comprehensive Core Loop analysis, several architectural patterns have emerged:

#### 1. **"Frontal Cortex" Pattern** (IMPLEMENTED ✅)
- **ConversationAgent**: Orchestrates external interfaces (speech, UI, state updates)
- **CoreLoopManager**: Handles internal processing and business logic
- **Clean separation**: External coordination vs. internal processing

#### 2. **Modular Extraction Strategy**
The successful CoreLoopManager extraction demonstrates a pattern for future refactoring:
- Start with utility functions (low risk)
- Extract state management (medium risk)
- Extract main orchestration (high risk)
- Maintain existing interfaces during transition

#### 3. **State Management Complexity**
Core Loop state involves multiple interconnected systems:
- **CoreLoopState**: Phase tracking, wish selection, completion status
- **CheckInState**: Themes, engagement metrics, stage progression
- **Conversation History**: Persistent across sessions
- **Wish Data**: Repository-managed manifestations

### Next Refactoring Targets

#### 1. **Concept Building Functions** (Immediate Next Step)
Functions still in ConversationAgent that should be extracted:
- `getWorkingPrompt()` - Concept screen prompt generation
- `getInitialPrompt()` - Initial concept building prompts
- `getNextBrainDecision()` - Concept screen LLM integration
- `initiateConceptBuilding()` - Concept screen entry point

#### 2. **Integration Challenges Identified**
- **Core Loop ↔ Concept Screen**: Need seamless transitions
- **State Synchronization**: Multiple state containers need coordination
- **LLM Integration**: Different prompt patterns for different contexts
- **Error Recovery**: Cross-module error handling strategies

### Long-term Vision

#### 1. **Complete Conversation Continuity**
- State persistence across app launches
- Intelligent resume vs. fresh start decisions
- Context-aware conversation restoration
- Progress tracking and analytics

#### 2. **Enhanced User Experience**
- Seamless transitions between conversation modes
- Personalized conversation flows based on history
- Adaptive conversation strategies based on engagement
- Rich progress visualization and insights

#### 3. **Robust Architecture**
- Clear module boundaries and responsibilities
- Comprehensive error handling and recovery
- Performance optimization for complex state management
- Extensible design for future conversation types
