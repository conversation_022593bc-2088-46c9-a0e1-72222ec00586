# ConversationAgent Architectural Map & Function Analysis

## Overview
This document provides both a comprehensive architectural map of the VoxManifestor conversation agent system and a systematic function mapping for `ConversationAgent.kt` refactoring. It defines the target modular architecture, data flows, and integration patterns.

## Target Architectural Structure

```
┌─────────────────────────────────────────────────────────────────┐
│                    CONVERSATION AGENT                           │
│                   (Central Orchestrator)                        │
│              ┌─────────────────────────────┐                    │
│              │     Core Input Router       │                    │
│              │  • handleVoiceInput()       │                    │
│              │  • toggleBrainConversation()│                    │
│              │  • monitorUiIntents()       │                    │
│              └─────────────────────────────┘                    │
└─────────────────────────────────────────────────────────────────┘
                                │
                ┌───────────────┼───────────────┐
                │               │               │
        ┌───────▼─────┐ ┌──────▼──────┐ ┌─────▼─────┐
        │VOICE MODULE │ │COMMAND MODULE│ │CORE LOOP  │
        │             │ │             │ │MODULE     │
        └─────────────┘ └─────────────┘ └───────────┘

┌─────────────────────────────────────────────────────────────────┐
│                    STATE MANAGEMENT LAYER                      │
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐           │
│  │ AgentCortex │  │StateManager │  │NavigationMgr│           │
│  │(State Hub)  │  │(Coordination)│  │(Routing)    │           │
│  └─────────────┘  └─────────────┘  └─────────────┘           │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                    DOMAIN-SPECIFIC MODULES                     │
│                                                                 │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐│
│ │  CHECK-IN   │ │ AFFIRMATION │ │   CONCEPT   │ │    TIMER    ││
│ │   SYSTEM    │ │   SYSTEM    │ │   SYSTEM    │ │   SYSTEM    ││
│ │             │ │             │ │             │ │             ││
│ │DialogueChain│ │AffirmMgr    │ │ConceptTools │ │TimerMgr     ││
│ │TransitionCh │ │AffirmTool   │ │ConceptBuild │ │TimerModels  ││
│ │CheckInSys   │ │AffirmOverlay│ │             │ │             ││
│ └─────────────┘ └─────────────┘ └─────────────┘ ┌─────────────┘│
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │             ││
│ │  UTILITIES  │ │ COMPONENTS  │ │ LEGACY FLOWS│ │             ││
│ │             │ │             │ │             │ │             ││
│ │ConvoUtils   │ │AgentPanels  │ │StateMachine │ │             ││
│ │HistoryMgr   │ │ProcessPanel │ │FlowFunctions│ │             ││
│ │ErrorHandler │ │AgentToolbar │ │             │ │             ││
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘│
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                     INTEGRATION LAYER                          │
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐           │
│  │BrainService │  │VoiceRecognit│  │TextToSpeech │           │
│  │(Gemini AI)  │  │Repository   │  │Repository   │           │
│  └─────────────┘  └─────────────┘  └─────────────┘           │
└─────────────────────────────────────────────────────────────────┘
```

## Data Flow Architecture

### 1. Voice Input Pipeline
```
User Voice Input
    ↓
VoiceRecognitionRepository (Google Speech-to-Text)
    ↓
ConversationAgent.handleVoiceInput()
    ↓
┌─────────────────┬─────────────────┐
│                 │                 │
▼                 ▼                 ▼
Commands Module   Core Loop Module  Check-In System
│                 │                 │
▼                 ▼                 ▼
Action Execution  LLM Processing    DialogueChain
```

### 2. Core Loop Progression Flow
```
UI Trigger (handleCoreLoopIntent)
    ↓
CoreLoopManager.initiateCoreLoopProcess()
    ↓
CoreLoopManager.progressCoreLoop()
    ↓
┌─────────────────┬─────────────────┐
│                 │                 │
▼                 ▼                 ▼
Phase Evaluation  LLM Integration   Wish Selection
│                 │                 │
▼                 ▼                 ▼
processPhaseWithLLM() → BrainService → AgentCortex
```

### 3. Check-In System Flow
```
progressCheckIn()
    ↓
CheckInSystem.DialogueChain
    ↓
┌─────────────────┬─────────────────┐
│                 │                 │
▼                 ▼                 ▼
Strategy Selection Response Generation Transition Evaluation
│                 │                 │
▼                 ▼                 ▼
CoachingTranscripts → BrainService → TransitionChain
```

## Module Specifications & Information Processing

### ConversationAgent (Central Orchestrator)
**Responsibilities**: Input routing, high-level conversation lifecycle, module coordination
**Information Processed**:
- Voice input → routes to appropriate module
- UI intents → delegates to specialized handlers  
- Conversation state → coordinates between modules
**Actions**: Route, delegate, coordinate, maintain conversation scope
**Size Target**: 200-400 lines

### Voice Module (`voice/`)
**Existing Files**: `VoiceProcessor.kt`, `VoiceFunctions.kt`
**Information Processed**:
- Raw audio → Google Speech-to-Text conversion
- Text responses → Google Text-to-Speech synthesis  
- Speech interruption signals → immediate stop commands
**Actions**: Convert speech, synthesize audio, manage speech state
**Integration**: Called directly by ConversationAgent for all speech operations

### Command Module (`commands/`)
**Existing Files**: `CommandProcessor.kt`, `CommandFunctions.kt`, `StateMachineFlows.kt`
**Information Processed**:
- Voice commands ("select", "define", "start", etc.) → action mapping
- Legacy state machine transitions → flow execution
- Command validation → success/failure responses
**Actions**: Parse commands, execute discrete actions, manage legacy flows
**Integration**: Receives voice input from ConversationAgent router

### Core Loop Module (`coreloop/` - To Be Created)
**Target Files**: `CoreLoopFunctions.kt`, `CoreLoopManager.kt`  
**Existing Tools**: `CoreLoopTools.kt`, `CoreLoopState.kt`, `WishPrioritization.kt`
**Information Processed**:
- Current phase state → next phase determination
- User responses → LLM prompt construction
- LLM responses → tool execution and state updates
- Wish priority data → next wish selection
**Actions**: Progress phases, integrate with BrainService, execute LLM tools, manage wish selection
**Integration**: Core orchestration called by ConversationAgent

### Check-In System (`checkin/`)
**Existing Files**: `DialogueChain.kt`, `CheckInSystem.kt`, `CoachingTranscripts.kt`, `TransitionChain.kt`
**Information Processed**:
- User emotional state → coaching strategy selection
- Conversation depth → chain progression decisions
- Theme extraction → transition evaluation
- Conversation context → personalized response generation
**Actions**: Execute 3-chain dialogue, extract themes, evaluate transitions, coordinate with Core Loop
**Integration**: Autonomous system called by ConversationAgent.progressCheckIn()

### Affirmation System (`affirmations/`)
**Existing Files**: `AffirmationManager.kt`, `AffirmationTool.kt`, `AffirmationOverlay.kt`
**Information Processed**:
- Manifestation goals → affirmation script generation
- Session parameters → timing and repetition control
- User engagement → overlay display management
**Actions**: Generate affirmations, manage sessions, coordinate UI overlay
**Integration**: Called by commands and Core Loop phases

### Concept System (`concept/` - Partial Implementation)
**Existing Files**: `ConceptTools.kt`
**Target Integration**: Screen lifecycle, LLM-driven exploration
**Information Processed**:
- Concept requests → tool library creation
- Exploration state → LLM decision trees
- Concept building → iterative refinement
**Actions**: Build concepts, manage exploration flows, integrate with BrainService
**Integration**: Screen-based system with ConversationAgent coordination

### State Management Layer
**Files**: `AgentCortex.kt`, `StateManager.kt`, `NavigationManager.kt`
**Information Processed**:
- All agent state changes → centralized state hub (AgentCortex)
- UI state updates → reactive flow propagation
- Navigation requests → screen routing coordination
**Actions**: Maintain state consistency, coordinate UI updates, manage navigation
**Integration**: Central coordination point for all modules

### Utilities & Components
**Utilities**: `ConversationUtilities.kt`, `HistoryTokenManager.kt`, `HistoryFormatter.kt`
**Components**: `AgentPanels.kt`, `AgentProcessPanel.kt`, `AgentToolbar.kt`, etc.
**Information Processed**:
- Conversation history → formatting and token management
- UI state → component rendering and interaction
- Error conditions → centralized error handling
**Actions**: Format data, render UI, handle errors, manage history
**Integration**: Shared services called by all modules

### Timer System (`timer/`)
**Existing Files**: `CheckInTimerManager.kt`, `TimerModels.kt`
**Information Processed**:
- Check-in timing → session duration management
- Timer state → UI coordination
- Timer events → conversation triggering
**Actions**: Manage check-in timing, coordinate with conversation flows
**Integration**: Event-driven integration with Check-In system

## Integration Patterns

### Module Communication
- **Direct Function Calls**: Voice, Command, Utilities modules
- **Event-Driven**: Timer → Check-In System
- **State-Mediated**: All modules ↔ AgentCortex
- **Service Integration**: All modules → BrainService for LLM operations

### Information Flow Hierarchies
1. **Input Processing**: Voice → Command routing → Module execution
2. **State Management**: Module actions → StateManager → AgentCortex → UI updates  
3. **AI Integration**: Module prompts → BrainService → Parsed responses → Module actions
4. **Conversation Coordination**: ConversationAgent orchestration → Module delegation → Result aggregation

## Function Categories & Proposed Module Placement

### 1. CORE LOOP ORCHESTRATION MODULE
**Domain**: Core Loop lifecycle management, phase progression, and LLM integration for structured manifestation workflow.

| Function | Line | Description | Notes |
|----------|------|-------------|-------|
| `handleCoreLoopIntent()` | 1922 | Initiates or resumes Core Loop conversations | Entry point from UI |
| `initiateCoreLoopProcess()` | 1953 | Sets up Core Loop state and conversation scope | Setup logic |
| `progressCoreLoop()` | 1984 | Main progression function, examines state and advances loop | Central orchestrator |
| `processPhaseWithLLM()` | 2497 | Processes phases using LLM guidance | LLM integration |
| `processCoreLoopPhase()` | 2731 | Creates prompts and handles LLM interactions | Duplicate of above? |
| `processCoreLoopResponse()` | 2775 | Processes LLM responses and executes tools | Response handling |
| `handlePhaseCompletion()` | 2827 | Marks phases complete and determines next phase | State transitions |
| `isValidPhaseTransition()` | 2891 | Validates phase transitions based on app logic | Validation logic |
| `markPhaseComplete()` | 2928 | Marks a phase as complete for current wish | State update |
| `handleCoreLoopResponse()` | 2947 | Handles user responses during Core Loop | Input processing |
| `createCoreLoopPrompt()` | 2297 | Creates prompts for Core Loop LLM | Prompt engineering |
| `createAgentContextInsights()` | 2173 | Creates context for current wish and phase | Context building |
| `createWishSelectionContext()` | 2260 | Creates context about why wish was selected | Context building |
| `selectNextWish()` | 2552 | Selects next wish using prioritization logic | Wish selection |
| `updateWishTimestamp()` | 2534 | Updates lastDiscussedTimestamp for wishes | State tracking |
| `resumeCoreLoop()` | 2629 | Resumes Core Loop where it left off | Conversation resume |
| `determineStartingPhase()` | 2696 | Determines appropriate starting phase for wish | Phase logic |

### 2. CHECK-IN SYSTEM MODULE  
**Domain**: Check-in dialogue management, theme extraction, and transition processing.

| Function | Line | Description | Notes |
|----------|------|-------------|-------|
| `progressCheckIn()` | 2012 | Main orchestration for check-in phase | DialogueChain coordinator |
| `handleContinueCheckIn()` | 2069 | Handles ContinueCheckIn outcome | Response handling |
| `handleTransition()` | 2095 | Handles transition from check-in to next phase | Transition processing |
| `handleTransitionStage()` | 2123 | Handles the transition stage specifically | Transition logic |
| `fetchUserWishesForContext()` | 247 | Fetches user wishes for check-in context | Data preparation |
| `handleTimerControl()` | 353 | Handles timer control intents | Timer management |

### 3. VOICE & INPUT PROCESSING MODULE
**Domain**: Voice recognition, speech synthesis, input parsing, and command processing.

| Function | Line | Description | Notes |
|----------|------|-------------|-------|
| `setVoiceManager()` | 704 | Captures voice manager for voice control | Dependency injection |
| `setupVoiceMonitoring()` | 720 | Sets up voice recognition monitoring | Initialization |
| `handleVoiceInput()` | 749 | Main voice input handler - routes to commands or conversation | Input router |
| `processBackgroundInput()` | 792 | Processes background voice input | Legacy input handling |
| `speak()` | 844 | Main speech synthesis function | Speech output |
| `interruptSpeech()` | 586 | Interrupts ongoing speech | Speech control |
| `cleanSpeech()` | 744 | Cleans speech text for comparison | Text processing |
| `processUserResponse()` | 1089 | Processes user responses (button press) | Input processing |
| `handleUserResponse()` | 1083 | Adds user response to conversation history | Response capture |

### 4. COMMAND PROCESSING MODULE
**Domain**: Voice command recognition and execution for discrete actions.

| Function | Line | Description | Notes |
|----------|------|-------------|-------|
| `handleSelectCommand()` | 942 | Handles 'select' voice command | Command handler |
| `handleDefineCommand()` | 954 | Handles 'define' voice command | Command handler |
| `handleStartCommand()` | 992 | Handles 'start' voice command | Command handler |
| `handleStopCommand()` | 1023 | Handles 'stop' voice command | Command handler |
| `handleReadCommand()` | 1042 | Handles 'read' voice command | Command handler |
| `handleInitiateCommand()` | 1069 | Handles 'initiate' voice command | Command handler |
| `handleAffirmCommand()` | 1557 | Handles 'affirm' voice command | Command handler |
| `handleQuitCommand()` | 1037 | Handles 'quit' voice command | Command handler |
| `handleHelpCommand()` | 874 | Handles 'help' voice command | Command handler |

### 5. LEGACY CONVERSATION FLOWS MODULE
**Domain**: State-machine driven wish collection and selection (to be replaced by Core Loop).

| Function | Line | Description | Notes |
|----------|------|-------------|-------|
| `enterConversationLoop()` | 1002 | Legacy entry point for conversations | Legacy flow |
| `proceedToNextWishStep()` | 1637 | Legacy wish capture flow | Legacy state machine |
| `proceedToNextSelectionStep()` | 1659 | Legacy wish selection flow | Legacy state machine |
| `handleProcessSelection()` | 1687 | Handles process selection for wishes | Legacy flow |
| `askForWish()` | 1725 | Asks user for wish input | Legacy interaction |
| `captureWish()` | 1734 | Captures wish from user | Legacy capture |
| `checkWish()` | 1739 | Validates captured wish | Legacy validation |
| `validateWish()` | 1860 | Final wish validation and storage | Legacy storage |
| `selectWishNumber()` | 1755 | Selects wish by number | Legacy selection |
| `selectWishBySlot()` | 1782 | Selects wish by slot index | Legacy selection |
| `agentDeleteWish()` | 1802 | Legacy wish deletion | Legacy operation |
| `agentEditWish()` | 1813 | Legacy wish editing | Legacy operation |
| `findNextEmptyWishSlot()` | 2989 | Finds next empty wish slot | Legacy utility |

### 6. CONCEPT BUILDING MODULE
**Domain**: Concept screen management and LLM-driven concept exploration.

| Function | Line | Description | Notes |
|----------|------|-------------|-------|
| `observeConceptViewModel()` | 408 | Observes concept screen loading state | Screen integration |
| `initiateConceptBuilding()` | 1149 | Initiates concept building process | Entry point |
| `getNextBrainDecision()` | 1224 | Gets next action from LLM for concept building | LLM integration |
| `getInitialPrompt()` | 1300 | Creates initial prompt for concept building | Prompt engineering |
| `getWorkingPrompt()` | 1476 | Creates working prompt for concept conversations | Prompt engineering |
| `askAndListen()` | 1529 | Asks question and sets up listening | Interaction helper |
| `createToolLibrary()` | 1129 | Creates ConceptToolLibrary instance | Tool management |
| `onConceptScreenExit()` | 620 | Handles concept screen exit | Screen lifecycle |
| `closeConceptScreen()` | 1897 | Closes concept screen programmatically | Navigation |

### 7. STATE MANAGEMENT MODULE
**Domain**: AgentCortex integration and state synchronization.

| Function | Line | Description | Notes |
|----------|------|-------------|-------|
| `changeDialogueState()` | 633 | Updates dialogue state via AgentCortex | State update |
| `changeDisplayState()` | 637 | Updates display state via AgentCortex | State update |
| `updateConceptActionState()` | 641 | Updates concept action state | State update |
| `updateConversationPlan()` | 645 | Updates conversation plan | State update |
| `updateConversationState()` | 1540 | Updates conversation type | State update |
| `updateCurrentStep()` | 1547 | Updates current conversation step | State update |
| `updateDialogueState()` | 1551 | Public wrapper for dialogue state update | State update |
| `resetConversation()` | 1615 | Resets conversation state variables | State reset |

### 8. CONVERSATION LIFECYCLE MODULE
**Domain**: Conversation startup, teardown, and scope management.

| Function | Line | Description | Notes |
|----------|------|-------------|-------|
| `toggleBrainConversation()` | 454 | Main conversation toggle (start/stop) | Central controller |
| `terminateConversation()` | 599 | Terminates conversation with cleanup | Cleanup logic |
| `monitorUiIntents()` | 294 | Monitors UI intents for agent actions | Intent handling |
| `monitorSlotSelection()` | 375 | Monitors slot selection changes | UI integration |

### 9. UTILITIES & HELPERS MODULE
**Domain**: Shared utility functions used across multiple modules.

| Function | Line | Description | Notes |
|----------|------|-------------|-------|
| `addToHistory()` | 651 | Adds entries to conversation history | History management |
| `logStatus()` | 276 | Logs status messages | Logging utility |
| `handleError()` | 196 | Centralized error handling | Error handling |
| `extractNumber()` | 1818 | Extracts numbers from text | Text parsing |
| `extractIntNumber()` | 1829 | Extracts integer numbers | Text parsing |
| `parseNumberWord()` | 1834 | Parses word numbers to integers | Text parsing |
| `resolveYesNo()` | 1847 | Resolves ambiguous yes/no responses | Input parsing |
| `getOrdinal()` | 1905 | Converts numbers to ordinal strings | Text formatting |
| `setCurrentSlotIndex()` | 1751 | Updates shared slot index state | State helper |

### 10. AFFIRMATION & SEQUENCING MODULE
**Domain**: Affirmation tool integration and conversation sequencing.

| Function | Line | Description | Notes |
|----------|------|-------------|-------|
| `executeSequence()` | 1581 | Executes conversation sequences | Sequence management |
| `stopCurrentSequence()` | 1609 | Stops current sequence execution | Sequence control |

### 11. NAVIGATION & INTEGRATION MODULE
**Domain**: Screen navigation and external component integration.

| Function | Line | Description | Notes |
|----------|------|-------------|-------|
| `setConceptNavigation()` | 166 | Sets concept screen navigation callback | Navigation setup |
| `setNavigateBack()` | 1893 | Sets back navigation callback | Navigation setup |

## Current Modular Architecture Status - UPDATED

```
ConversationAgent (Orchestrator - 1,779 lines → Target: 1,200-1,400 lines)
├── ✅ VoiceProcessor (Lines 114-119) - EXTRACTED
│   ├── ✅ Voice input/output management
│   ├── ✅ Speech synthesis
│   └── ✅ Input parsing utilities
├── ✅ CommandMode (Lines 121-127) - EXTRACTED
│   ├── ✅ Voice command handlers
│   ├── ✅ Legacy wish collection/selection flows
│   └── ✅ Command routing logic
├── ✅ StateManager (Lines 98) - EXTRACTED
│   ├── ✅ AgentCortex integration
│   └── ✅ State synchronization
├── ✅ NavigationManager (Lines 97) - EXTRACTED
│   ├── ✅ Concept screen navigation
│   └── ✅ Screen routing
├── ✅ AffirmationManager (Lines 106-112) - EXTRACTED
│   ├── ✅ Affirmation generation
│   ├── ✅ Session management
│   └── ✅ UI overlay coordination
├── ✅ CoreLoopManager (Lines 1723-1728) - EXTRACTED
│   ├── ✅ Phase progression logic
│   ├── ✅ LLM integration for Core Loop
│   └── ✅ Core loop prompt generation
├── 🔄 ConceptScreenManager (~420 lines) - TO EXTRACT
│   ├── 📍 observeConceptViewModel() (Lines 395-440)
│   ├── 📍 initiateConceptBuilding() (Lines 854-926)
│   ├── 📍 getNextBrainDecision() (Lines 933-1007)
│   ├── 📍 getInitialPrompt() (Lines 1013-1183)
│   ├── 📍 getWorkingPrompt() (Lines 1189-1242)
│   ├── 📍 createToolLibrary() (Lines 834-842)
│   ├── 📍 askAndListen() (Lines 1244-1252)
│   ├── 📍 onConceptScreenExit() (Lines 618-631)
│   └── 📍 closeConceptScreen() (Lines 1295-1301)
└── 🎯 FINAL EXTRACTION TARGET: ~420 lines remaining
```

## Implementation Priority & Current Status

### ✅ COMPLETED PHASES - MAJOR PROGRESS:
1. **Phase 1**: ✅ **VoiceProcessor** - EXTRACTED (`voice/VoiceProcessor.kt`)
2. **Phase 2**: ✅ **CommandMode** - EXTRACTED (`commands/CommandMode.kt`)
3. **Phase 3**: ✅ **StateManager** - EXTRACTED (`state/StateManager.kt`)
4. **Phase 4**: ✅ **NavigationManager** - EXTRACTED (`navigation/NavigationManager.kt`)
5. **Phase 5**: ✅ **AffirmationManager** - EXTRACTED (`affirmations/AffirmationManager.kt`)
6. **Phase 6**: ✅ **CoreLoopManager** - EXTRACTED (`coreloop/CoreLoopManager.kt`)

### 🔄 FINAL REMAINING PHASE:
7. **Phase 7**: Extract `ConceptScreenManager` - **ONLY REMAINING EXTRACTION**
   - **Target**: ~420 lines of concept building functions
   - **Completion**: Will reduce ConversationAgent to ~1,360 lines
   - **Impact**: Completes the modularization effort

### 📊 CURRENT METRICS - UPDATED:
- **ConversationAgent.kt**: 1,779 lines (significantly reduced from ~3,000+ lines)
- **Extracted Modules**: 6 major modules completed (VoiceProcessor, CommandMode, StateManager, NavigationManager, AffirmationManager, CoreLoopManager)
- **Reduction**: ~1,200+ lines extracted to specialized modules

## Legacy Code Assessment

The following functions represent legacy flows that should eventually be replaced by Core Loop phases:
- All functions in **LEGACY CONVERSATION FLOWS MODULE** 
- `enterConversationLoop()` and related state machine logic
- Command-driven wish collection/selection flows

These can be isolated into a `LegacyFlowManager` during transition and eventually deprecated.

## Dependencies & Coupling

- **High Coupling**: Core Loop ↔ Check-In (shared state, transition logic)
- **Medium Coupling**: Voice Processing ↔ Command Processing (input routing)
- **Low Coupling**: Concept Building ↔ Core Loop (separate screens)

## Next Steps & Recommendations

### 🎯 IMMEDIATE PRIORITY: Phase 7 - ConceptScreenManager (FINAL EXTRACTION)
**Target Functions** (confirmed from actual code analysis):
- `observeConceptViewModel()` (Lines 395-440) - Screen lifecycle management
- `initiateConceptBuilding()` (Lines 854-926) - Concept building entry point  
- `getNextBrainDecision()` (Lines 933-1007) - LLM integration for concept building
- `getInitialPrompt()` (Lines 1013-1183) - Initial prompt generation
- `getWorkingPrompt()` (Lines 1189-1242) - Working prompt generation
- `createToolLibrary()` (Lines 834-842) - Tool library creation
- `askAndListen()` (Lines 1244-1252) - Question asking helper
- `onConceptScreenExit()` (Lines 618-631) - Screen exit handling
- `closeConceptScreen()` (Lines 1295-1301) - Screen closing helper
- **Estimated Size**: ~420 lines
- **Complexity**: Medium (screen lifecycle, LLM integration, tool management)

### 🔄 ARCHITECTURAL ACHIEVEMENTS - MAJOR PROGRESS:
1. ✅ **Complete modularization** - 6 major modules successfully extracted
2. ✅ **AgentCortex maintained** as single source of truth for state
3. ✅ **Dependency injection implemented** for all service references
4. ✅ **Public API preserved** during all transitions
5. ✅ **Legacy flows isolated** in CommandMode
6. ✅ **Core Loop extracted** - CoreLoopManager handles phase progression
7. ✅ **Voice processing extracted** - VoiceProcessor handles all speech operations
8. ✅ **State management extracted** - StateManager coordinates AgentCortex
9. ✅ **Navigation extracted** - NavigationManager handles screen routing
10. ✅ **Affirmations extracted** - AffirmationManager handles affirmation workflows

### 📋 FINAL REMAINING WORK:
- **ConceptScreenManager extraction** - Only remaining modularization task (~420 lines)
- **Integration testing** - Verify all extracted modules work correctly together
- **Documentation updates** - Update architecture docs to reflect completed extractions