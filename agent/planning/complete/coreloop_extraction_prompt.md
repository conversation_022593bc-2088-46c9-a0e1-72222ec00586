# CoreLoopManager Extraction Prompt

## Objective
Extract Core Loop functionality from ConversationAgent into a dedicated CoreLoopManager module while maintaining the "frontal cortex" architecture where ConversationAgent retains control of all external interfaces (speech, state, UI, databases).

## Architectural Philosophy: The "Frontal Cortex" Pattern

### ConversationAgent as the "Frontal Cortex"
The ConversationAgent acts as the conscious, decision-making center that:
- **Controls all external interfaces**: Speech (TTS/STT), UI interactions, database operations
- **Orchestrates conversation flow**: Decides when to delegate and when to take control back
- **Manages attention and focus**: When user attention/focus is needed, control returns to ConversationAgent
- **Delegates processing tasks**: Hands off complex processing to specialized modules
- **Processes returned results**: Takes action based on what submodules return

### Submodules as "Processing Engines"
Submodules (CoreLoopManager, DialogueChain, etc.) act as specialized processors that:
- **Perform complex internal processing**: LLM integration, algorithm execution, data analysis
- **Return results, don't act directly**: Provide outcomes for Conversation<PERSON><PERSON> to act upon
- **No external interface access**: Cannot speak, update UI, or manage databases directly
- **Focus on domain logic**: Handle their specific domain without orchestration concerns

## The Check-In Pattern: Proven Model to Follow

### How Check-In Currently Works (The Correct Pattern)
```kotlin
// 1. ConversationAgent orchestrates and delegates
ConversationAgent.progressCheckIn()
  → checkInDialogueChain.processTurn(userWishes) // Delegate processing

// 2. DialogueChain processes internally (no external interface access)
    → [Internal: 4-chain process, LLM calls, theme extraction, strategy selection]
    → return DialogueChainOutcome(text, themes, metrics, transitionDecision) // Return results

// 3. ConversationAgent processes results and controls external interfaces
  → ConversationAgent.handleContinueCheckIn(outcome) // Agent takes control back
    → speak(outcome.text) // AGENT speaks (not DialogueChain)
    → addToHistory(Speaker.Agent, outcome.text) // AGENT manages history
    → agentCortex.updateActiveThemes(outcome.themes) // AGENT manages state
    → agentCortex.updateCheckInState(outcome.metrics) // AGENT manages state
```

### Key Pattern Elements
1. **Delegate Processing**: ConversationAgent calls submodule for complex work
2. **Return Results**: Submodule returns structured outcome with message + data
3. **Agent Controls Interfaces**: ConversationAgent handles speech, state, history
4. **Agent Decides Next Steps**: ConversationAgent processes outcome and orchestrates next action

## Extraction Principles

### Functions to EXTRACT to CoreLoopManager (Processing Engine Functions)
**Core Loop Phase Processing (Phases 2-7 Only):**
- `processPhaseWithLLM()` - LLM integration for WISH_COLLECTION through LOOP_DECISION phases
- `handlePhaseCompletion()` - Phase completion logic and next phase determination
- `createCoreLoopPrompt()` - Prompt engineering for Core Loop phases
- `processCoreLoopResponse()` - LLM response processing and tool execution
- `selectNextWish()` - Wish selection algorithm and prioritization logic

**Internal Processing Functions:**
- Core Loop state analysis and phase progression logic
- Wish prioritization algorithms and completion tracking
- LLM prompt construction and response parsing
- Tool execution coordination (without direct external interface access)

**Result Generation:**
- Return `CoreLoopOutcome` objects with messages and state updates
- No direct speech, state updates, or history management
- Focus on domain logic and algorithmic processing

### Functions to KEEP in ConversationAgent (Frontal Cortex Functions)
**External Interface Control (Agent's Exclusive Domain):**
- `speak()` - ALL speech originates from ConversationAgent (never from submodules)
- `addToHistory()` - ALL conversation history management through ConversationAgent
- `agentCortex.updateCoreLoopState()` - ALL state updates through ConversationAgent
- `changeDialogueState()` - ALL UI state changes through ConversationAgent
- Database operations via repository - ALL data persistence through ConversationAgent

**Check-In Orchestration (Following Proven Pattern):**
- `progressCheckIn()` - Check-In phase orchestration (delegates to DialogueChain, processes results)
- `handleTransition()` - Check-In → Core Loop transition processing
- `handleTransitionStage()` - Transition stage management and user interaction
- `handleCoreLoopIntent()` - Core Loop entry point coordination

**High-Level Conversation Flow:**
- `toggleBrainConversation()` - Main conversation lifecycle management
- `progressCoreLoop()` - Core Loop orchestration (delegates to CoreLoopManager, processes results)
- `handleCoreLoopOutcome()` - Process CoreLoopManager results and take action
- `stopBrainConversation()` - Conversation termination and cleanup

**Input Processing & Routing:**
- `handleVoiceInput()` - Route voice input to appropriate processor
- `monitorUiIntents()` - Process UI events and delegate appropriately
- `processUserResponse()` - Route user responses based on conversation context

**Cross-Module Coordination:**
- Voice processing delegation to VoiceProcessor
- Command processing delegation to CommandMode
- State management coordination with AgentCortex
- Conversation scope management (`conversationScope`)
- Error handling and recovery coordination

### Functions to REMOVE/CONSOLIDATE
- `processCoreLoopPhase()` - Duplicate of `processPhaseWithLLM()` (remove)
- `initiateCoreLoopProcess()` - Move setup logic to CoreLoopManager constructor
- Unnecessary delegation wrappers that add no value

## CoreLoopManager Interface Design (Following Check-In Pattern)

### Result Objects (Like DialogueChainOutcome)
```kotlin
sealed class CoreLoopOutcome {
    data class ContinuePhase(
        val message: String,
        val stateUpdate: CoreLoopState? = null,
        val reasoning: String = ""
    ) : CoreLoopOutcome()

    data class PhaseComplete(
        val message: String,
        val completedPhase: ConversationPhase,
        val nextPhase: ConversationPhase,
        val stateUpdate: CoreLoopState,
        val reasoning: String = ""
    ) : CoreLoopOutcome()

    data class WishSelectionNeeded(
        val message: String,
        val availableWishes: List<WishSummary>,
        val selectionCriteria: String = ""
    ) : CoreLoopOutcome()

    data class ConversationComplete(
        val message: String,
        val finalState: CoreLoopState
    ) : CoreLoopOutcome()
}

data class WishSelectionResult(
    val selectedWishIndex: Int,
    val selectionReasoning: String,
    val nextPhase: ConversationPhase
)
```

### CoreLoopManager Interface (Processing Engine - No External Interface Access)
```kotlin
class CoreLoopManager(
    private val agentCortex: AgentCortex,
    private val brainService: BrainService,
    private val repository: ManifestationRepository,
    private val conversationRepository: ConversationRepository,
    private val logStatus: (String, StatusColor) -> Unit // Only for internal logging
) {

    // Main Processing Functions (Return Results, Don't Act)
    suspend fun processCurrentPhase(): CoreLoopOutcome
    suspend fun processPhaseWithLLM(phase: ConversationPhase): CoreLoopOutcome
    suspend fun handlePhaseCompletion(phase: ConversationPhase): CoreLoopOutcome
    suspend fun selectNextWish(): WishSelectionResult

    // Internal Processing (No External Interface Access)
    suspend fun createCoreLoopPrompt(phase: ConversationPhase): String
    suspend fun processCoreLoopResponse(response: String, currentState: CoreLoopState): CoreLoopOutcome
    suspend fun analyzePhaseProgress(phase: ConversationPhase): PhaseAnalysis

    // NO ACCESS TO:
    // - speak() ❌ (ConversationAgent handles all speech)
    // - addToHistory() ❌ (ConversationAgent manages history)
    // - changeDialogueState() ❌ (ConversationAgent manages UI state)
    // - agentCortex.updateCoreLoopState() ❌ (ConversationAgent manages state)
}
```

## ConversationAgent Orchestration Flow Analysis

### Current Input Processing Pipeline
```kotlin
// 1. UI Intent Processing (monitorUiIntents)
AgentCortex.UiIntent.RequestToggleConversation → toggleBrainConversation()
AgentCortex.UiIntent.InitiateCoreLoop → handleCoreLoopIntent()
AgentCortex.UiIntent.SendResponse → processUserResponse()

// 2. Voice Input Processing (handleVoiceInput)
VoiceCommandEntity + text → Route based on currentConversation:
  - ConversationType.CoreLoop → handleUserResponse() → handleCoreLoopResponse()
  - ConversationType.ConceptBuilding → handleUserResponse() → getNextBrainDecision()
  - null/CommandMode → Command routing (SELECT, START, STOP, etc.)

// 3. Background Input Processing (processBackgroundInput)
DialogueState.ExpectingInput + ConversationType.CommandMode → commandMode.processInput()
```

### State Management Coordination
```kotlin
// ConversationAgent maintains these orchestration states:
private var conversationScope: CoroutineScope? = null
private val currentConversation: ConversationType? get() = agentCortex.conversationType.value

// State transitions coordinated through:
updateConversationState(ConversationType.CoreLoop) → stateManager.updateConversationType()
changeDialogueState(DialogueState.ExpectingInput) → agentCortex.updateDialogueState()
```

### Core Loop Integration Points

#### Entry Points to CoreLoopManager
1. **UI Toggle**: `toggleBrainConversation()` → `handleCoreLoopIntent()`
2. **Direct Intent**: `UiIntent.InitiateCoreLoop` → `handleCoreLoopIntent(startPhase)`
3. **Voice Command**: "start" → should route to `handleCoreLoopIntent()` (currently broken)

#### State Inputs CoreLoopManager Needs
```kotlin
// From AgentCortex (read-only access)
- coreLoopState: StateFlow<CoreLoopState>
- checkInState: StateFlow<CheckInState>
- conversationHistory: StateFlow<List<ConversationEntry>>

// From ConversationAgent (injected functions)
- speak: suspend (String) -> Unit
- addToHistory: suspend (Speaker, String, ConversationPhase) -> Unit
- changeDialogueState: (DialogueState) -> Unit
- logStatus: (String, StatusColor) -> Unit
```

#### State Outputs CoreLoopManager Returns
```kotlin
// State updates (via AgentCortex)
agentCortex.updateCoreLoopState(newState)
agentCortex.updateCheckInState(newCheckInState)

// Conversation flow control (via injected functions)
changeDialogueState(DialogueState.ExpectingInput)
changeDialogueState(DialogueState.Speaking)
```

### Response Processing Pipeline
```kotlin
// Current flow in ConversationAgent:
handleVoiceInput() → handleUserResponse() → processUserResponse()
  ↓
when (currentConversation) {
    ConversationType.CoreLoop → handleCoreLoopResponse(text)
    ConversationType.ConceptBuilding → getNextBrainDecision()
}

// After extraction:
handleVoiceInput() → handleUserResponse() → processUserResponse()
  ↓
when (currentConversation) {
    ConversationType.CoreLoop → coreLoopManager.handleUserResponse(text)
    ConversationType.ConceptBuilding → getNextBrainDecision()
}
```

## Integration Pattern

### CoreLoopManager Dependency Injection
```kotlin
class CoreLoopManager(
    private val agentCortex: AgentCortex,
    private val brainService: BrainService,
    private val repository: ManifestationRepository,
    private val conversationRepository: ConversationRepository,
    private val checkInDialogueChain: CheckInDialogueChain,
    private val checkInTimerManager: CheckInTimerManager,
    // Injected orchestration functions
    private val speak: suspend (String) -> Unit,
    private val addToHistory: suspend (Speaker, String, ConversationPhase) -> Unit,
    private val changeDialogueState: (DialogueState) -> Unit,
    private val logStatus: (String, StatusColor) -> Unit
)
```

### ConversationAgent Orchestration Pattern (Following Check-In Model)
```kotlin
// In ConversationAgent
private lateinit var coreLoopManager: CoreLoopManager

// Initialize without external interface functions
private fun initializeCoreLoopManager() {
    coreLoopManager = CoreLoopManager(
        agentCortex = agentCortex,
        brainService = brainService,
        repository = repository,
        conversationRepository = conversationRepository,
        logStatus = ::logStatus // Only internal logging
        // NO speak, addToHistory, changeDialogueState - Agent retains control
    )
}

// Check-In stays in ConversationAgent (proven pattern)
private suspend fun progressCheckIn() {
    // Agent orchestrates Check-In (existing pattern works)
    val outcome = checkInDialogueChain.processTurn(fetchUserWishesForContext())

    when (outcome) {
        is DialogueChainOutcome.ContinueCheckIn -> handleContinueCheckIn(outcome)
        is DialogueChainOutcome.InitiateTransition -> handleTransition(outcome)
    }
}

// Transition handling stays in ConversationAgent
private suspend fun handleTransitionStage() {
    // Agent speaks transition message
    speak("Let's focus on your goals...")
    addToHistory(Speaker.Agent, "Let's focus on your goals...", ConversationPhase.CHECK_IN)

    // Agent selects wish and hands off to Core Loop
    val wishResult = coreLoopManager.selectNextWish()

    // Agent speaks wish selection message
    speak("I'd like to explore ${wishResult.selectionReasoning}")
    addToHistory(Speaker.Agent, "I'd like to explore ${wishResult.selectionReasoning}", ConversationPhase.CHECK_IN)

    // Agent updates state and starts Core Loop phases
    agentCortex.updateCoreLoopState(
        agentCortex.coreLoopState.value.copy(
            currentWishIndex = wishResult.selectedWishIndex,
            currentPhase = wishResult.nextPhase
        )
    )

    // Agent continues orchestrating
    progressCoreLoop()
}

// Core Loop orchestration (delegate processing, handle results)
private suspend fun progressCoreLoop() {
    val currentState = agentCortex.coreLoopState.value

    // Skip Check-In (handled separately)
    if (currentState.currentPhase == ConversationPhase.CHECK_IN) {
        progressCheckIn()
        return
    }

    // Delegate Core Loop processing to CoreLoopManager
    val outcome = coreLoopManager.processCurrentPhase()

    // Agent processes results and controls external interfaces
    handleCoreLoopOutcome(outcome)
}

// Agent processes CoreLoopManager results (like handleContinueCheckIn)
private suspend fun handleCoreLoopOutcome(outcome: CoreLoopOutcome) {
    when (outcome) {
        is CoreLoopOutcome.ContinuePhase -> {
            // Agent speaks and manages state
            speak(outcome.message)
            addToHistory(Speaker.Agent, outcome.message, agentCortex.coreLoopState.value.currentPhase)
            outcome.stateUpdate?.let { agentCortex.updateCoreLoopState(it) }
            changeDialogueState(DialogueState.ExpectingInput(VoxInputType.BRAIN_RESPONSE))
        }

        is CoreLoopOutcome.PhaseComplete -> {
            // Agent handles phase completion
            speak(outcome.message)
            addToHistory(Speaker.Agent, outcome.message, outcome.completedPhase)
            agentCortex.updateCoreLoopState(outcome.stateUpdate)

            // Agent continues with next phase
            progressCoreLoop()
        }

        is CoreLoopOutcome.WishSelectionNeeded -> {
            // Agent handles wish selection
            speak(outcome.message)
            // Agent coordinates wish selection process
        }

        is CoreLoopOutcome.ConversationComplete -> {
            // Agent handles conversation completion
            speak(outcome.message)
            agentCortex.updateCoreLoopState(outcome.finalState)
            stopBrainConversation()
        }
    }
}
```

### Conversation Scope Management
```kotlin
// ConversationAgent retains scope management responsibility
private var conversationScope: CoroutineScope? = null

// Scope lifecycle tied to conversation start/stop
private suspend fun toggleBrainConversation() {
    if (hasActiveConversation) {
        // Stop conversation
        conversationScope?.cancel()
        conversationScope = null
        updateConversationState(null)
    } else {
        // Start conversation
        conversationScope = CoroutineScope(SupervisorJob() + Dispatchers.Default)
        updateConversationState(ConversationType.CoreLoop)
        conversationScope?.launch {
            coreLoopManager.start()
        }
    }
}
```

### Error Handling & Recovery
```kotlin
// ConversationAgent handles high-level error recovery
private suspend fun handleCoreLoopIntent() {
    conversationScope?.launch {
        try {
            coreLoopManager.start()
        } catch (e: CancellationException) {
            logStatus("Core Loop conversation was cancelled", StatusColor.Pause)
            // ConversationAgent handles scope cleanup
        } catch (e: Exception) {
            logStatus("Exception in Core Loop: ${e.message}", StatusColor.Stop)
            // ConversationAgent decides recovery strategy
            handleError(e)
        }
    }
}
```

## Extraction Steps (Following Check-In Pattern)

1. **Create CoreLoopOutcome result objects** (like DialogueChainOutcome)
2. **Create CoreLoopManager class** with limited dependencies (no external interface access)
3. **Extract ONLY processing functions** (phases 2-7, no Check-In transition logic)
4. **Update extracted functions** to return CoreLoopOutcome instead of direct actions
5. **Remove duplicate functions** (`processCoreLoopPhase()`)
6. **Keep Check-In orchestration** in ConversationAgent (proven pattern)
7. **Update ConversationAgent** to process CoreLoopOutcome results
8. **Create handleCoreLoopOutcome()** method (like handleContinueCheckIn)
9. **Fix voice command routing** ("start" → Core Loop instead of CommandMode)
10. **Test integration** ensuring speech/state/history all flow through ConversationAgent

## ConversationAgent Responsibilities After Extraction

### High-Level Orchestration (KEEP)
- **Input Routing**: `handleVoiceInput()` → route to appropriate module
- **Conversation Lifecycle**: `toggleBrainConversation()` → start/stop conversations
- **Scope Management**: Create/cancel `conversationScope` for conversation sessions
- **State Coordination**: Update `ConversationType` and `DialogueState`
- **Error Recovery**: Handle exceptions and decide recovery strategies
- **Module Integration**: Coordinate between Voice, Command, Core Loop, Concept modules

### UI Intent Processing (KEEP)
- **Monitor UI Intents**: `monitorUiIntents()` → process AgentCortex.UiIntent events
- **Delegate to Modules**: Route intents to appropriate specialized handlers
- **State Synchronization**: Ensure UI state reflects conversation state

### Cross-Module Coordination (KEEP)
- **Voice Processing**: Delegate to VoiceProcessor for speech synthesis/recognition
- **Command Processing**: Delegate to CommandMode for discrete voice commands
- **State Management**: Coordinate with AgentCortex for centralized state
- **Navigation**: Handle screen transitions and concept screen lifecycle

### Infrastructure Services (KEEP)
- **Conversation History**: `addToHistory()` → centralized history management
- **Speech Services**: `speak()` → centralized speech synthesis
- **Logging**: `logStatus()` → centralized status logging
- **Error Handling**: `handleError()` → centralized error processing

## Success Criteria

### Functional Requirements
- ✅ ~600 lines extracted from ConversationAgent to CoreLoopManager
- ✅ All existing Core Loop functionality preserved
- ✅ Duplicate functions removed (`processCoreLoopPhase()`)
- ✅ Voice command routing fixed ("start" → Core Loop)
- ✅ Clean separation of concerns (orchestration vs. implementation)

### Integration Requirements (Frontal Cortex Pattern)
- ✅ ConversationAgent retains ALL external interface control (speech, state, history, UI)
- ✅ CoreLoopManager handles ONLY internal processing (returns results, doesn't act)
- ✅ Check-In orchestration remains in ConversationAgent (proven pattern)
- ✅ Result flow: CoreLoopManager → CoreLoopOutcome → ConversationAgent → External actions
- ✅ Error handling: CoreLoopManager exceptions → ConversationAgent recovery
- ✅ Conversation scope management remains in ConversationAgent
- ✅ Pattern consistency with Check-In system (DialogueChain → DialogueChainOutcome)

### Quality Requirements
- ✅ No compilation errors or functionality regressions
- ✅ Dependency injection pattern properly implemented
- ✅ Function signatures updated to use injected dependencies
- ✅ All tests pass (if existing)
- ✅ Code follows existing patterns and conventions
