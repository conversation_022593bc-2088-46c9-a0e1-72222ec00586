# MVP July Launch v1 - Product Requirements Document

## Background and Motivation

Following user testing and reflection on the current check-in functionality, we're shifting toward a **focused MVP approach** that prioritizes a polished, usable experience over feature breadth. The check-in system demonstrates strong AI conversation capabilities and theme extraction - these core strengths should be the foundation for the first user-ready version.

**Strategic Focus**: Create a workable app that one close friend can start using, establishing the foundation for user feedback and iterative development.

## Key Challenges and Analysis

### Current Strengths to Preserve
- **Check-in conversation system** with sophisticated theme extraction
- **Transition analysis** providing valuable user insights
- **Dialogue chain architecture** (proven stable)
- **Voice-driven interaction** model

### Identified User Experience Gaps
- **Conversation visibility**: Difficulty seeing conversation history and agent's last response
- **Theme accessibility**: Extracted themes are valuable but not easily reviewable
- **Goal management**: No conversational way to create/edit the 5 main life goals
- **Session management**: Unclear patterns for resuming vs. starting fresh

### Technical Considerations
- Leverage existing conversation infrastructure
- Build on proven AgentCortex state management
- Maintain voice-first interaction paradigm
- Avoid complex concept screen integration for MVP

## High-level Task Breakdown

### Phase 1: Enhanced Conversation Experience
**Priority: Critical**

1. **Conversation History Enhancement**
   - Modify main screen layout to prominently display agent's last utterance
   - Create scrollable conversation history view on main screen
   - Ensure transition messages are properly logged and visible
   - **Success Criteria**: User can always see what the agent said last and review recent conversation

2. **Theme Visualization System**
   - Create dedicated theme display screen/popup
   - Show extracted themes in organized list format
   - Include ability to view themes from previous sessions
   - **Success Criteria**: User can easily review and understand conversation themes

3. **Conversation Replay Features**
   - Add "repeat last response" functionality
   - Create "summarize themes" command for agent
   - Enable easy access to agent's analysis
   - **Success Criteria**: User can request clarification of agent insights

### Phase 2: Goal Management Integration
**Priority: High**

4. **Conversational Goal Creation/Editing**
   - Implement LLM-driven goal definition process
   - Mirror original voice command pattern but with conversational AI
   - Enable editing of the 5 main life goals through dialogue
   - Replace basic CRUD operations with guided conversation
   - **Success Criteria**: User can create and modify goals entirely through conversation

5. **Main Screen Goal Display Enhancement**
   - Show current goals prominently on main screen
   - Integrate goal status with conversation context
   - Display last-updated timestamps for goals
   - **Success Criteria**: User has clear visibility into their goal state

### Phase 3: Session Management
**Priority: Medium**

6. **Check-in Session Control**
   - Add "Start New Check-in" button to main screen
   - Implement session continuity vs. fresh start decision logic
   - Create clear user choice between continuing vs. restarting
   - **Success Criteria**: User can intentionally start fresh check-ins or continue previous sessions

7. **Session Resume Enhancement**
   - Enable viewing themes when resuming previous conversations
   - Preserve conversation context across app sessions
   - Improve conversation history persistence
   - **Success Criteria**: Resuming conversations feels natural and contextual

### Phase 4: Polish and Launch Preparation
**Priority: Medium**

8. **User Experience Refinements**
   - Improve main screen layout for conversation visibility
   - Add helpful voice command hints
   - Implement smooth transitions between conversation modes
   - **Success Criteria**: App feels polished and intuitive for first-time users

9. **Launch Readiness**
   - Basic error handling for common failure modes
   - Simple onboarding flow
   - Essential help/guidance features
   - **Success Criteria**: App is stable enough for friend testing

## Success Criteria

### Core Functionality
- **Conversational Goal Management**: User can create, view, and edit their 5 life goals entirely through conversation with the agent
- **Enhanced Check-in Experience**: User can easily see conversation history, review themes, and understand agent insights
- **Session Control**: User can choose between continuing previous conversations or starting fresh check-ins

### User Experience Metrics
- **Conversation Visibility**: User never loses track of what the agent said
- **Theme Accessibility**: User can review extracted themes within 2 taps/commands
- **Goal Clarity**: User's 5 life goals are always visible and contextual

### Technical Stability
- **Error Resilience**: App handles common failure modes gracefully
- **State Persistence**: Conversations and themes persist across app sessions
- **Performance**: Voice interactions feel responsive and natural

## Strategic Decision Points

### Question: Session Continuity vs. Fresh Start
**Decision Needed**: How should the app handle the choice between continuing previous sessions vs. starting new check-ins?

**Options**:
- **Simple Toggle**: "Continue last session" vs. "Start fresh" button choice
- **Contextual Detection**: Agent asks user's preference based on session age/completeness
- **Smart Default**: Continue recent sessions (<24hrs), default to fresh for older sessions

**Recommendation**: Start with simple toggle approach for MVP, evolve based on user feedback.

### Question: Concept Screen Integration Timeline
**Decision Needed**: Should concept screen functionality be included in MVP or deferred?

**Analysis**: Based on feedback focusing on "workable version" and goal management, concept screen integration should be **deferred to post-MVP**. Focus on perfecting the main screen experience first.

**Recommendation**: Keep concept screen functional but don't enhance it for MVP. Focus entirely on main screen conversational experience.

## Post-MVP Roadmap Preview

### Immediate Post-Launch (August)
- **Concept Screen Integration**: Flow check-in themes into concept exploration
- **Advanced Theme Analysis**: Pattern recognition across multiple sessions
- **Voice Command Expansion**: Additional conversational capabilities

### Medium-term (September-October)
- **Multi-user Support**: Sharing and collaboration features
- **Advanced Analytics**: Progress tracking and insights
- **Platform Expansion**: Additional device support

## Implementation Notes

### Architectural Considerations
- **Leverage Existing Infrastructure**: Build on AgentCortex, CheckInDialogueChain, and ConversationAgent
- **Incremental Enhancement**: Enhance existing components rather than rebuilding
- **Voice-First Design**: Maintain conversational interaction as primary paradigm

### Development Priorities
1. **User Experience First**: Prioritize visibility and usability improvements
2. **Proven Patterns**: Use successful conversation patterns for goal management
3. **Iterative Testing**: Test each enhancement with target user before proceeding

### Risk Mitigation
- **Scope Creep Prevention**: Focus ruthlessly on defined MVP features
- **Technical Debt Management**: Clean up as you enhance, don't accumulate debt
- **User Feedback Integration**: Plan for rapid iteration based on friend testing

---

**Next Steps**: Review this PRD and confirm priority alignment before beginning Phase 1 implementation.