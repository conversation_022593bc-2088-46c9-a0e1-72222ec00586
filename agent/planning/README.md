# Planning Directory

This directory contains strategic planning documents, requirements analysis, and project roadmaps for VoxManifestor.

## Contents

### Product Requirements Documents (PRDs)
- **[mvp_launch_prd.md](mvp_launch_prd.md)** - Focused MVP launch requirements with critical issues, 5-week roadmap, and launch criteria
- **[post_mvp_roadmap.md](post_mvp_roadmap.md)** - Comprehensive post-MVP strategic roadmap including gamification, cross-platform strategy, and long-term vision


## Directory Purpose

The `agent/planning/` directory serves as the central repository for:

1. **Strategic Documentation**: High-level product strategy and roadmaps
2. **Requirements Analysis**: Detailed feature requirements and user story mapping
3. **Implementation Planning**: Technical planning documents and architecture decisions
4. **Process Improvement**: Documentation standards and development workflow refinements

## Relationship to Other Directories

- **`agent/planning/`** ← *You are here* - Strategic documents and requirements
- **`agent/context/`** - Implementation context and technical documentation  

## Usage Guidelines

### When to Add Documents Here
- Product requirements documents (PRDs)
- Feature specifications and user story mappings
- Technical planning and architecture decision records
- Project roadmaps and milestone planning
- Process improvement proposals

### When NOT to Add Documents Here
- Technical implementation details (use `agent/context/` instead)
- Task tracking and execution (use `agent/tasks/` instead)  
- Code quality rules (use `.cursor/rules/` instead)
- User-facing documentation (use `docs/` if created)

### Document Naming Conventions
- Use descriptive names with underscores: `feature_planning.md`
- Include document type when relevant: `authentication_prd.md`
- Date stamp for time-sensitive docs: `q1_2024_roadmap.md`
- Use `.md` format for structured documents, `.txt` for simple notes



---

*Keep this directory focused on planning and strategy. Move implementation artifacts to appropriate technical directories as they mature.* 