## Document Conventions

   * REQUIRED SCRATCHPAD SECTIONS:
     - "Background and Motivation": Clearly articulate the problem being solved and its importance
     - "Key Challenges and Analysis": Document technical considerations, constraints, and analysis of alternative approaches
     - "High-level Task Breakdown": Provide numbered, sequential steps with clear completion criteria
     - "Success Criteria": Define measurable outcomes that indicate successful implementation
   
- The `agent/scratchpad.md` file is divided into several sections. Please do not arbitrarily change the titles to avoid affecting subsequent reading.

- "Background and Motivation" and "Key Challenges and Analysis": self-explanatory

- "High-level Task Breakdown": step-by-step implementation plan. When in Executor mode, complete one step at a time stop while the human user verifies it. Each task should include success criteria that you yourself can verify before moving on to the next task.

- "Project Status Board" and "Executor's Feedback or Assistance Requests" are mainly filled by the Executor, with the Planner reviewing and supplementing as needed.

- "Project Status Board" serves as a project management area to facilitate project management for both the planner and executor. It follows simple markdown todo format.

* When Tasks are Completed and scratchpad updated, Summarize them enough that the completed task list is concise enough to be added to `CHANGELOG.md`